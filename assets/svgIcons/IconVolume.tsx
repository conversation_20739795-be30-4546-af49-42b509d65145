import * as React from 'react';
import Svg, {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, G, <PERSON>, SvgProps} from 'react-native-svg';

const IconVolume: React.FC<SvgProps> = props => (
  <Svg width={45} height={44} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#535862"
        d="M24.81 6.048c1.278-.912 3.03-.078 3.18 1.431l.01.21V36.31c0 1.569-1.697 2.51-3.012 1.755l-.177-.114-12.065-8.618H7.833a3.667 3.667 0 0 1-3.656-3.393l-.01-.274v-7.333a3.667 3.667 0 0 1 3.393-3.657l.273-.01h4.913L24.81 6.048Zm11.746 6.386A12.806 12.806 0 0 1 40.833 22c0 3.8-1.655 7.217-4.277 9.565a1.833 1.833 0 1 1-2.445-2.732A9.14 9.14 0 0 0 37.166 22a9.14 9.14 0 0 0-3.056-6.833 1.833 1.833 0 0 1 2.445-2.733Zm-3.667 4.1A7.32 7.32 0 0 1 35.333 22a7.32 7.32 0 0 1-2.444 5.465 1.833 1.833 0 0 1-2.607-2.568l.162-.164A3.653 3.653 0 0 0 31.667 22c0-.978-.38-1.864-1.006-2.523l-.217-.21a1.833 1.833 0 1 1 2.445-2.733Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.5 0h44v44H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default IconVolume;
