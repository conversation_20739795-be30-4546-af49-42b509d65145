import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {scale} from 'react-native-size-matters';
import {heightScreen, widthScreen} from '../../../src/utils/Scale';
import {BubbleTail} from '../../svgIcons/BubbleTail';
import TextApp from '../../../src/components/TextApp';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import useSound from '../../../src/hooks/useSound';

type Props = {
  contentBubble: {content: string; sound: string};
};

const BUBBLE_WIDTH = scale(265);

export const ChatBubble = ({contentBubble}: Props) => {
  const [key, setKey] = useState(0);
  const scaleValue = useSharedValue(0.5);
  const opacity = useSharedValue(0);
  const {stop, playLocalFile} = useSound();

  useEffect(() => {
    setKey(prev => prev + 1);
    stop();
    playLocalFile(contentBubble?.sound, false);
    scaleValue.value = 0.5;
    opacity.value = 0;

    const duration = 1500;

    scaleValue.value = withTiming(1, {
      duration,
      easing: Easing.out(Easing.exp),
    });

    opacity.value = withTiming(1, {
      duration,
      easing: Easing.out(Easing.exp),
    });
    return () => {
      stop();
    };
  }, [contentBubble]);

  const animatedStyle = useAnimatedStyle(() => {
    const translateX = -(1 - scaleValue.value) * (BUBBLE_WIDTH / 2);
    return {
      transform: [{translateX}, {scale: scaleValue.value}],
      opacity: opacity.value,
    };
  });

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.bubble, animatedStyle]}>
        <TextApp
          key={key}
          preset="text_md_medium"
          text={contentBubble?.content}
          style={styles.content}
        />
        <View style={styles.tailWrapper}>
          <BubbleTail />
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: (750 / 2436) * heightScreen,
    zIndex: 1,
    left: (240 / 1125) * widthScreen,
    alignItems: 'flex-start',
  },
  bubble: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: scale(14),
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 6,
    elevation: 3,
    width: BUBBLE_WIDTH,
    alignSelf: 'flex-start',
  },
  content: {
    color: '#000',
    textAlign: 'center',
    lineHeight: 20,
  },
  tailWrapper: {
    position: 'absolute',
    bottom: -15,
    left: 40,
    transform: [{translateX: -10}],
  },
});
