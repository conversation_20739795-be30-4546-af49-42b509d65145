import * as React from 'react';
import {useRef} from 'react';
import {G, Image} from 'react-native-svg';
import {GestureResponderEvent} from 'react-native';

interface ItemHandleProps {
  x: number;
  y: number;

  onItemPress: () => void;
  status: number;
}

const TAP_THRESHOLD = 10;
const ItemHandle: React.FC<ItemHandleProps> = ({
  x,
  y,

  onItemPress,
  status,
}) => {
  const startX = useRef(0);
  const startY = useRef(0);
  const isTapValid = useRef(false);

  const handlePressIn = (e: GestureResponderEvent) => {
    const {locationX, locationY} = e.nativeEvent;
    startX.current = locationX;
    startY.current = locationY;
    isTapValid.current = true;
  };

  const handleMove = (e: GestureResponderEvent) => {
    const {locationX, locationY} = e.nativeEvent;
    const dx = Math.abs(locationX - startX.current);
    const dy = Math.abs(locationY - startY.current);
    if (dx > TAP_THRESHOLD || dy > TAP_THRESHOLD) {
      isTapValid.current = false;
    }
  };

  const handlePressOut = (e: GestureResponderEvent) => {
    if (isTapValid.current) {
      onItemPress();
    }
    isTapValid.current = false;
  };
  if (status == 2) {
    return (
      <G
        transform={`translate(${x}, ${y})`}
        onStartShouldSetResponder={() => true}
        onMoveShouldSetResponder={() => true}
        onResponderGrant={handlePressIn}
        onResponderMove={handleMove}
        onResponderRelease={handlePressOut}>
        <Image
          id="image0_206_1003"
          width={224}
          height={183}
          preserveAspectRatio="none"
          href={require('./done.png')}
        />
      </G>
    );
  }
  if (status == 0) {
    return (
      <G
        transform={`translate(${x}, ${y})`}
        onStartShouldSetResponder={() => true}
        onMoveShouldSetResponder={() => true}
        onResponderGrant={handlePressIn}
        onResponderMove={handleMove}
        onResponderRelease={handlePressOut}>
        <Image
          id="image0_206_1003"
          width={224}
          height={183}
          preserveAspectRatio="none"
          href={require('./inactive.png')}
        />
      </G>
    );
  }
  if (status == 1) {
    return (
      <G
        transform={`translate(${x}, ${y})`}
        onStartShouldSetResponder={() => true}
        onMoveShouldSetResponder={() => true}
        onResponderGrant={handlePressIn}
        onResponderMove={handleMove}
        onResponderRelease={handlePressOut}>
        <Image
          id="image0_206_1003"
          width={224}
          height={183}
          preserveAspectRatio="none"
          href={require('./active.png')}
        />
      </G>
    );
  }
  return <G />;
};
export default ItemHandle;
