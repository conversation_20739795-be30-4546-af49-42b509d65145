import React from 'react';
import {StyleSheet} from 'react-native';
import {G, Image, Text} from 'react-native-svg';
import {FontFamily} from '../../../src/themes/typography';

type Props = {
  id: string;
  y: number;
  missionName: string;
  numOfPass: number;
  numOfQuestion: number;
};

export const MissionCompletedItem = ({
  id,
  y,
  missionName,
  numOfPass,
  numOfQuestion,
}: Props) => {
  const match = missionName.match(/^(.*)\s(\d{2}:\d{2}\s\d{2}\/\d{2}\/\d{4})$/);
  const title = match?.[1] ?? missionName;
  const datetime = match?.[2] ?? '';

  const isDone = numOfPass === numOfQuestion && numOfQuestion > 0;

  const missionIcon = isDone
    ? require('./mission-done.webp')
    : require('./mission-notdone.webp');

  return (
    <G id={id}>
      <Image
        x={64.941}
        y={y}
        width={252}
        height={72}
        preserveAspectRatio="none"
        href={require('./bg-mission-completed.webp')}
      />
      <G id="Frame 427320191">
        <G id="Frame 427320190">
          <G id="Mission Icons" clipPath="url(#clip2_1_1036)">
            <Image
              x={80}
              y={y + 10}
              width={48}
              height={48}
              preserveAspectRatio="none"
              href={missionIcon}
            />
          </G>
        </G>
      </G>
      <G id="Frame 427320189">
        <Text
          x={147.484}
          y={y + 27}
          fontWeight={'bold'}
          textAnchor="start"
          fontSize={16}
          fontFamily={FontFamily.bold}
          fill={'#6A2600'}>
          {title}
        </Text>
        <Text
          x={147.484}
          y={y + 53}
          fontWeight={'bold'}
          textAnchor="start"
          fontSize={16}
          fontFamily={FontFamily.bold}
          fill={'#6A2600'}>
          {datetime}
        </Text>
      </G>
    </G>
  );
};

const styles = StyleSheet.create({
  text: {
    position: 'absolute',
    fontSize: 16,
    fontFamily: FontFamily.bold,
    fontWeight: 'bold',
    color: '#6A2600',
  },
});
