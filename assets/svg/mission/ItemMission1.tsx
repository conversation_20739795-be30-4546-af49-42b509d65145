import * as React from 'react';
import Svg, {ClipPath, Defs, G, Image, Path, SvgProps} from 'react-native-svg';
import {Dimensions, View} from 'react-native';
import BtnMission from './BtnMission.tsx';

const {width, height} = Dimensions.get('screen');

interface ItemMission1 extends SvgProps {
  data: Array<any>;
  onItemPress: (item: any, index: number) => void;
  active: number;
}

const dataBtn = [
  {id: 'btnM_6', x: 530, y: 1785},
  {id: 'btnM_5', x: 228, y: 1401},
  {id: 'btnM_4', x: 209, y: 936},
  {id: 'btnM_3', x: 702, y: 1415},
  {id: 'btnM_2', x: 784, y: 1001},
  {id: 'btnM_1', x: 590, y: 588},
];

const ItemMission1: React.FC<ItemMission1> = ({onItemPress, data, active}) => {
  return (
    <View
      style={{
        width,
        height,
      }}>
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 1179 2556"
        preserveAspectRatio="none"
        fill="none">
        <G id="m1" clipPath="url(#clip0_392_173)">
          <Image
            x={0}
            y={0}
            id="image0_206_1003"
            width={1179}
            height={2556}
            preserveAspectRatio="none"
            href={require('./bgItem1.png')}
          />
          <G id="Group">
            <Path
              id="Vector"
              fill="#FDDE5F"
              d="M2181.92 2058.08c20.37-31.13 27.01-95.74 19.71-132.21-1.74-8.67-12 14.46-20.84 14.31-31.63-.54-66.52-7.45-97.76-2.44.75 74.79-44.76 143.1-68.75 210.06-7.29 20.36-28.19 33.38-49.45 37.35-21.26 3.97-43.13.47-64.47-3.03-86.67-14.22-173.33-28.45-260-42.67-38.48-6.32-77.49-12.79-112.98-28.94-51.14-23.28-92.73-66.81-113.65-118.97-9.22-23-14.54-48.59-8.44-72.61 6.3-24.83 24.02-45.18 43.23-62.13 56.56-49.92 129.47-78.43 185.92-128.47 98.98-87.72 132.63-227.04 147.81-358.42 5.29-45.82 7.24-97.53-23.29-132.11-12.24-13.86-28.53-23.37-44.52-32.65-24.2-14.04-48.41-28.08-72.61-42.12-26.47-15.35-55.2-33.52-63.41-63-5.36-19.25-.76-39.85-3.21-59.69-5.54-44.87-44.99-77.1-83.21-101.25-115.78-73.15-246.95-117.58-376.72-161.35-137.35-46.33-282.18-93.28-424.65-66.58-35.27 6.61-71.33 18.7-95.75 44.99-39.31 42.32-36.55 112.2-5.47 160.88 31.08 48.68 84.33 79.04 138.79 98.29 35.71 12.62 74.74 22.47 100.5 50.22 17.18 18.5.15 82.35 3.97 107.3 6.25 40.82 29.08 71.43 58.16 100.75 42.69 43.04 96.6 60.68 110.51 119.68 3.95 16.75 5.98 36.26-5.07 49.46-14.48 17.3-43.62 14.47-59.35 30.64-7.61 7.82-10.79 18.86-16.87 27.91-12.09 18.02-34.84 26.51-56.53 26.87-21.69.36-42.84-6.21-63.54-12.72-29.96-9.42-61.77-20.07-81.19-44.75-5.92-7.52-10.44-16.09-16.5-23.51-27.09-33.17-76.69-35.67-112.86-58.61-36.1-22.88-56-64.46-67.18-105.71-11.18-41.25-15.57-84.34-30.08-124.55-14.5-40.21-41.9-78.93-82.46-92.41-5.74-1.91-11.64-3.28-17.57-4.49-39.39-8.06-80.89-9.14-119.31 2.67s-83.41 37.64-101.39 73.6c-13.32 26.65-22.37 94.81-22.15 124.61.95 126.49 80.31 209.85 172.58 296.38 24.95 23.4 52.92 45.92 65.67 77.65 4.68 11.64 7.08 24.07 11.39 35.86 17.47 47.84 40.95 105.14 80.98 136.64 40.02 31.5 101.9 46.92 100.26 97.82-.45 13.94-17.31 20.32-31.04 22.79-163.37 29.42-270.35-145.44-421.25-133.17-.19.01-.38.02-.54.04-.03 0-.06 0-.09.01-.11.01-.22.02-.31.04-31.73 5.26-62.81 11.82-95.15 11.74-10.58-.02-21.17-.57-31.7-1.64v135.31c25.2 2.07 51.15 3.07 76.31 1.68a263.11 263.11 0 0 0 61.37-10.76c1.21-.36 2.35-.74 3.45-1.15 11.03-4.04 28.11-1.13 39.35.76 11.33 1.91 22.93 5.85 33.78 9.68 33.43 11.8 55.07 38.9 81.58 60.47 26.12 21.24 57.89 34.79 90.41 42.74 36.94 9.03 75.06 13.43 113.09 12.57 71.24-1.62 146.16-22.23 205.05-62.96 27.8-19.23 62.52-43.01 64.27-80.28 1.03-21.85-9.9-42.71-24.07-59.37-12.16-14.31-26.77-26.31-41.28-38.23-45.31-37.2-90.62-74.41-135.94-111.61-14.17-11.64-29.17-24.53-33.54-42.34-2.17-8.86-1.47-18.14-2.43-27.21-2.68-25.31-17.95-47.26-32.74-67.97-30.23-42.34-60.6-84.85-96.86-122.16-13.76-14.16-28.33-27.52-42.17-41.6-35.49-36.09-60.87-82.02-72.54-131.28-5.69-24.01-8.06-49.8.49-72.95 8.55-23.15 30.29-42.73 54.97-42.43 29.93.36 52.9 28.91 59.6 58.08 6.7 29.17 1.74 59.56 1.33 89.49-.55 41.02 8.51 83.91 34.98 115.26 49.98 59.2 138.62 67.79 187.96 127.52 68.77 83.25 166.85 124.73 272.79 103.81 22.17-4.38 70.03-18.81 87.97-32.54 17.94-13.73 31.17-35.03 30.79-57.62-.14-8.61-.93-19.66 6.9-23.25 42.17-19.31 95.97-26.75 118.09-67.51 9.19-16.93 10.85-36.95 9.87-56.19-3.13-61.18-32.33-120.6-78.84-160.47-22.22-19.04-48.12-33.78-68.45-54.84-20.33-21.06-34.76-51.4-26.39-79.44 4.15-13.9 13.39-25.67 19.33-38.9 13.26-29.56 8.19-66.47-12.56-91.36-20.65-24.77-53.26-36.14-84.98-41.94-31.72-5.8-64.43-7.22-95.06-17.31-40.9-13.48-76.4-42.66-97.53-80.17-4.28-7.6-8.1-16.22-6.48-24.8 4.41-23.41 34.72-29.61 58.55-29.61 111.89.02 223.72 22.72 326.77 66.32 28.34 11.99 56 25.53 83.63 39.06 96.29 47.15 192.58 94.29 288.88 141.44 16.92 8.29 21.88 30.44 19.73 49.16-2.15 18.72-9.28 36.94-8.38 55.77 1.58 33.12 27.87 60.33 57.03 76.12 29.16 15.79 62.05 23.36 92.14 37.29 30.09 13.94 58.99 36.9 66.71 69.15 6.4 26.75-2.87 54.48-12.03 80.41-30.66 86.77-62.97 176.02-125.12 243.9-32.17 35.13-71.24 63.23-111.43 88.81-37.33 23.76-76.18 45.81-108.78 75.73-32.6 29.92-58.98 69.3-63.15 113.35-3.53 37.22 15.49 73.07 38.99 102.14 23.51 29.08 52.04 54.06 73.13 84.94 11.89 17.41 21.31 36.55 34.68 52.85 26.03 31.75 65.27 50.33 105.3 59.46 40.03 9.14 81.42 9.76 122.44 11.52 90.67 3.9 181.1 13.59 270.54 28.98 61.87 10.65 126.98 23.8 186.08 2.61 21.78-7.81 43.38-21.93 50.63-43.9 10.78-32.67-13.9-67.7-8.52-101.68 4.35-27.45 26.93-47.71 42.15-70.97l-.01.06Z"
            />
            <G id="Group_2">
              <Path
                id="Vector_2"
                fill="#D66112"
                d="M535.11 1794.74c19.89 20.23 39.16 40.98 61.55 58.52 22.04 17.26 45.02 33.26 69.35 47.14 5.59 3.19 11.27 6.21 16.91 9.32 4.6 2.53 10.01 4.26 11.81 9.65 3 9-3.74 15.38-10.82 19.72-20.36 12.48-44.6 18.76-67.59 24.21-32.54 7.72-65.18 14.31-98.47 17.82-5.7.6-5.76 9.61 0 9 31.71-3.34 62.8-9.34 93.84-16.5 25.53-5.89 51.76-12.53 74.74-25.57 7.69-4.36 15.78-10.63 17.68-19.79 2.13-10.25-3.11-19.03-11.94-23.81-25.81-13.95-50.76-28.87-74.39-46.32-14.18-10.46-27.86-21.47-40.61-33.64-12.25-11.68-23.83-24.05-35.69-36.11-4.07-4.14-10.43 2.23-6.36 6.36h-.01Z"
              />
              <Path
                id="Vector_3"
                fill="#D66112"
                d="M484.01 1731.78c8.06 11.28 16.78 22.07 25.06 33.19 3.42 4.59 11.24.11 7.77-4.54-8.28-11.12-17-21.9-25.06-33.19-3.34-4.67-11.15-.18-7.77 4.54Z"
              />
              <Path
                id="Vector_4"
                fill="#D66112"
                d="M444.67 1673.43c5.52 9.05 11.3 17.94 17.38 26.63 3.29 4.71 11.1.22 7.77-4.54-6.07-8.69-11.85-17.58-17.38-26.63-3.01-4.94-10.8-.42-7.77 4.54Z"
              />
              <Path
                id="Vector_5"
                fill="#D66112"
                d="M294.39 1411.48c11.88 14.81 23.88 29.52 35.56 44.48 11.68 14.96 23.4 29.24 33.06 45.26 9.66 16.02 15.92 31.72 22.48 48.22 6.94 17.45 14.18 34.79 21.93 51.9 8.79 19.43 18.26 38.55 28.55 57.24 2.79 5.07 10.57.54 7.77-4.54-18.52-33.63-34.25-68.67-48.49-104.29-6.67-16.7-13.22-33.49-22.16-49.15-8.94-15.66-20.35-30.03-31.53-44.29-13.47-17.18-27.15-34.18-40.81-51.2-3.62-4.52-9.96 1.89-6.36 6.36v.01Z"
              />
              <Path
                id="Vector_6"
                fill="#D66112"
                d="M264.62 1373.16c6.11 8.22 12.36 16.33 18.68 24.37 3.58 4.55 9.91-1.86 6.36-6.36-5.85-7.45-11.63-14.95-17.28-22.55-3.42-4.6-11.23-.11-7.77 4.54h.01Z"
              />
              <Path
                id="Vector_7"
                fill="#D66112"
                d="M219.36 1299.83c4.82 15.59 15.14 28.16 23.69 41.78 3.07 4.89 10.86.38 7.77-4.54-8.09-12.89-18.22-24.88-22.78-39.63-1.7-5.52-10.4-3.16-8.68 2.39Z"
              />
              <Path
                id="Vector_8"
                fill="#D66112"
                d="M205.62 1214.13c-1.53 17.18-.46 34.33 2.82 51.26 1.1 5.68 9.78 3.28 8.68-2.39-3.13-16.13-3.95-32.49-2.5-48.86.51-5.77-8.49-5.73-9 0v-.01Z"
              />
              <Path
                id="Vector_9"
                fill="#D66112"
                d="M234.39 1136.11c-9.66 12.6-16.87 26.76-21.33 42.01-1.63 5.57 7.05 7.95 8.68 2.39 4.25-14.53 11.23-27.85 20.43-39.86 3.52-4.6-4.29-9.08-7.77-4.54h-.01Z"
              />
              <Path
                id="Vector_10"
                fill="#D66112"
                d="M422.53 1114.75c-24.12-21.65-57.83-32.13-89.9-30.53-8.5.43-16.9 1.74-25.23 3.44-3.88.79-7.03 2.09-10.54 3.82-3.51 1.73-7.68 2.91-11.43 4.51-14.5 6.17-27.7 14.97-38.75 26.23-4.06 4.14 2.3 10.51 6.36 6.36 9.97-10.17 21.78-18.16 34.81-23.91 3.52-1.55 7.14-2.73 10.71-4.13 3.57-1.4 6.53-3.2 10.37-4.03 7.8-1.69 15.71-2.89 23.69-3.29 29.67-1.49 61.15 7.79 83.54 27.89 4.3 3.86 10.68-2.48 6.36-6.36h.01Z"
              />
              <Path
                id="Vector_11"
                fill="#D66112"
                d="M458.46 1170.69c-5.36-15.78-13.26-30.24-23.79-43.16-3.66-4.49-9.99 1.91-6.36 6.36 9.5 11.66 16.65 24.97 21.48 39.19 1.85 5.45 10.55 3.11 8.68-2.39h-.01Z"
              />
              <Path
                id="Vector_12"
                fill="#D66112"
                d="M466.94 1217.94a174.14 174.14 0 0 0-3.82-30.69c-1.22-5.66-9.9-3.26-8.68 2.39 2.02 9.35 3.17 18.74 3.5 28.3.2 5.78 9.2 5.8 9 0Z"
              />
              <Path
                id="Vector_13"
                fill="#D66112"
                d="M466.23 1282.4c-.09-10.17.3-20.31 1.13-30.44.47-5.77-8.53-5.74-9 0-.83 10.13-1.22 20.27-1.13 30.44.05 5.79 9.05 5.8 9 0Z"
              />
              <Path
                id="Vector_14"
                fill="#D66112"
                d="M483.38 1369.15c-5.97-14.8-10.87-29.97-13.5-45.75-.95-5.7-9.62-3.28-8.68 2.39 2.63 15.77 7.52 30.94 13.5 45.75 2.14 5.3 10.85 2.99 8.68-2.39Z"
              />
              <Path
                id="Vector_15"
                fill="#D66112"
                d="M546.29 1441.36c-19.23-5.92-36.81-17.16-47.78-34.33-3.11-4.87-10.9-.36-7.77 4.54 12.25 19.18 31.64 31.84 53.16 38.46 5.55 1.71 7.92-6.98 2.39-8.68v.01Z"
              />
              <Path
                id="Vector_16"
                fill="#D66112"
                d="M616.36 1454.71c-10.96-3.2-22.32-4.51-33.4-7.18-5.63-1.36-8.03 7.32-2.39 8.68 11.08 2.67 22.45 3.98 33.4 7.18 5.57 1.63 7.95-7.06 2.39-8.68Z"
              />
              <Path
                id="Vector_17"
                fill="#D66112"
                d="M680.91 1525.31c-9.83-15.65-20.7-30.6-31.25-45.76-3.28-4.72-11.09-.23-7.77 4.54 10.55 15.17 21.42 30.11 31.25 45.76 3.07 4.89 10.86.38 7.77-4.54Z"
              />
              <Path
                id="Vector_18"
                fill="#D66112"
                d="M993.42 1480.76c-10.2 2.62-18.73 9.36-24.26 18.26-2.69 4.33-4.71 9.28-5.69 14.29-.45 2.3-.6 4.61-.84 6.93-.29 2.81-1.46 5.25-1.88 8.03-1.96 12.83-1.81 25.35-7.44 37.43-5.21 11.17-15.1 17.32-25.95 22.5-22.17 10.59-47.18 12.27-71.34 10.51-24.62-1.8-49.76-5.97-73.84-11.4-27.98-6.31-54.1-17.97-77.5-34.55-4.74-3.35-9.24 4.45-4.54 7.77 20.71 14.67 43.71 25.72 68.16 32.56 12.41 3.47 25.07 5.55 37.75 7.75 12.29 2.13 24.6 4.18 36.99 5.62 24.19 2.82 49.25 3.13 72.73-4.16 11.59-3.59 24.19-8.9 33.61-16.72 9.23-7.66 14.23-18.91 16.94-30.44 1.59-6.76 2.4-13.71 2.95-20.62.26-3.23.72-5.84 1.66-8.89.98-3.22.6-6.64 1.23-9.93 2.35-12.35 11.35-23.11 23.67-26.27 5.61-1.44 3.23-10.12-2.39-8.68l-.02.01Z"
              />
              <Path
                id="Vector_19"
                fill="#D66112"
                d="M1083.4 1458.83c-10.11 16.95-28.65 22.24-47.39 22.04-5.79-.06-5.8 8.94 0 9 21.97.24 43.41-6.8 55.16-26.5 2.98-4.99-4.8-9.52-7.77-4.54Z"
              />
              <Path
                id="Vector_20"
                fill="#D66112"
                d="M1083.79 1376.01c7.48 16.11 11.01 33.54 9.79 51.29-.4 5.78 8.6 5.76 9 0 1.33-19.36-2.87-38.3-11.01-55.83-2.44-5.25-10.2-.68-7.77 4.54h-.01Z"
              />
              <Path
                id="Vector_21"
                fill="#D66112"
                d="M1044 1328.04c7.95 3.85 13.37 11.2 19.51 17.31 4.11 4.09 10.48-2.27 6.36-6.36-6.68-6.65-12.66-14.52-21.33-18.71-5.19-2.51-9.76 5.25-4.54 7.77v-.01Z"
              />
              <Path
                id="Vector_22"
                fill="#D66112"
                d="M975.43 1281.77c11.86 7.2 22.73 15.89 34.82 22.72 5.05 2.85 9.59-4.92 4.54-7.77-12.09-6.83-22.96-15.52-34.82-22.72-4.96-3.01-9.49 4.77-4.54 7.77Z"
              />
              <Path
                id="Vector_23"
                fill="#D66112"
                d="M872.43 998.771c20.59.93 43.17 4.209 60.42 16.359 8.36 5.89 14.45 14.31 16.77 24.32 1.36 5.87 1.79 12.22-1.05 17.73-3.27 6.35-10.25 9.08-16.16 12.43-13.05 7.4-26.13 14.87-37.63 24.59-5.64 4.76-9.22 9.33-12.37 15.99-2.62 5.55-4.72 11.34-6.16 17.31-5.71 23.77-1.33 48.63 8.79 70.57 11.76 25.48 31.52 47.55 55.6 61.96 4.98 2.98 9.51-4.8 4.54-7.77-41.69-24.95-74.29-79.09-58.61-128.27 1.95-6.12 4.54-13.47 8.9-18.3 5.08-5.63 11.87-10.21 18.15-14.37 6.28-4.16 12.94-8.02 19.55-11.79 6.13-3.49 13.41-6.49 18.47-11.51 16.98-16.89 4.97-45.59-11.1-58.28-18.88-14.909-44.75-18.899-68.12-19.959-5.79-.26-5.78 8.74 0 9l.01-.01Z"
              />
              <Path
                id="Vector_24"
                fill="#D66112"
                d="M805.63 984.29c16.1 5.47 32.64 9.24 49.41 11.99 5.67.93 8.1-7.74 2.39-8.68-16.76-2.75-33.31-6.52-49.41-11.99-5.5-1.87-7.85 6.82-2.39 8.68Z"
              />
              <Path
                id="Vector_25"
                fill="#D66112"
                d="M726.94 953.64c12.51 9.28 26.64 16.18 41.85 19.69 5.64 1.3 8.04-7.37 2.39-8.68-14.53-3.35-27.75-9.921-39.7-18.791-4.65-3.45-9.15 4.35-4.54 7.77v.011Z"
              />
              <Path
                id="Vector_26"
                fill="#D66112"
                d="M675.8 898.411c6.17 8.52 12.02 17.26 18.62 25.46 3.63 4.51 9.96-1.89 6.36-6.36-6.11-7.6-11.49-15.75-17.21-23.64-3.37-4.65-11.18-.16-7.77 4.54Z"
              />
              <Path
                id="Vector_27"
                fill="#D66112"
                d="M826.54 746.16c-40.94-3.38-82.2-3.91-123.08.54-4.93.54-9.85 1.15-14.76 1.82-5.22.72-9.77 2.47-14.8 3.95-9.11 2.68-18.89 4.04-27.54 8.1-15.34 7.19-14.29 29.16-12.77 43.11 2.35 21.45 10.43 40.85 19.1 60.4 2.34 5.28 10.1.71 7.77-4.54-7-15.78-13.99-31.74-16.93-48.87-1.44-8.39-2.08-16.98-1.4-25.48.71-8.94 3.71-15.24 12.56-18.18 8.28-2.75 16.92-4.23 25.2-6.99 8.91-2.96 18.1-3.8 27.46-4.74 18.48-1.87 37.06-2.74 55.63-2.81 21.22-.07 42.42.93 63.56 2.67 5.77.48 5.74-8.53 0-9v.02Z"
              />
              <Path
                id="Vector_28"
                fill="#D66112"
                d="M907.53 753.611c-15.45-2.34-30.96-4.06-46.56-4.9-5.79-.31-5.77 8.69 0 9 14.8.8 29.52 2.36 44.17 4.58 5.67.86 8.11-7.81 2.39-8.68Z"
              />
              <Path
                id="Vector_29"
                fill="#D66112"
                d="M998.32 773.531c-18.65-5.24-37.63-9.14-56.47-13.59-5.63-1.33-8.04 7.34-2.39 8.68 18.84 4.45 37.82 8.36 56.47 13.59 5.59 1.57 7.97-7.11 2.39-8.68Z"
              />
              <Path
                id="Vector_30"
                fill="#D66112"
                d="M1027.55 789.471c7.91 1.58 15.63 3.86 23.04 7.07 5.26 2.28 9.85-5.48 4.54-7.77-8.16-3.53-16.47-6.24-25.19-7.98-5.66-1.13-8.08 7.55-2.39 8.68Z"
              />
              <Path
                id="Vector_31"
                fill="#D66112"
                d="M1079.66 806.56c7.01 3.61 14.01 7.23 21.02 10.84 5.14 2.65 9.7-5.11 4.54-7.77-7.01-3.61-14.01-7.23-21.02-10.84-5.14-2.65-9.7 5.11-4.54 7.77Z"
              />
              <Path
                id="Vector_32"
                fill="#D66112"
                d="M1129.83 830.72c9.16 3.83 18.33 7.66 27.49 11.5 5.34 2.23 7.67-6.47 2.39-8.68-9.16-3.83-18.33-7.66-27.49-11.5-5.34-2.23-7.67 6.47-2.39 8.68Z"
              />
            </G>
            <Path
              id="Vector_33"
              fill="#D66112"
              d="M17.35 1873.02c16.03 2.43 32.13 2.95 48.26 1.28 5.7-.59 5.76-9.6 0-9-15.28 1.58-30.67 1.35-45.87-.96-5.67-.86-8.11 7.81-2.39 8.68Z"
            />
            <Path
              id="Vector_34"
              fill="#D66112"
              d="M95.77 1871.54c15.34.56 30.58-1.61 45.16-6.43 5.47-1.81 3.13-10.51-2.39-8.68-13.88 4.59-28.15 6.64-42.76 6.11-5.79-.21-5.78 8.79 0 9h-.01Z"
            />
            <Path
              id="Vector_35"
              fill="#D66112"
              d="M172.64 1862.37c14.49.27 32.43-4.03 44.89 5.65 4.52 3.51 10.93-2.81 6.36-6.36-15.12-11.74-33.49-7.96-51.26-8.28-5.79-.11-5.79 8.89 0 9l.01-.01Z"
            />
            <Path
              id="Vector_36"
              fill="#D66112"
              d="M244.01 1882.96c14.17 12.51 28.57 25.05 45.6 33.54 5.17 2.58 9.73-5.18 4.54-7.77-16.39-8.17-30.15-20.1-43.78-32.13-4.33-3.82-10.72 2.52-6.36 6.36Z"
            />
            <Path
              id="Vector_37"
              fill="#D66112"
              d="M318.16 1934.66c12.2 10.83 25.85 19.7 40.79 26.28 5.25 2.31 9.84-5.44 4.54-7.77-14.23-6.26-27.34-14.55-38.97-24.87-4.32-3.83-10.71 2.51-6.36 6.36Z"
            />
            <Path
              id="Vector_38"
              fill="#D66112"
              d="M380.47 1978.19c12.5 7 26.39 10.55 40.73 10.34 5.79-.08 5.8-9.08 0-9-12.78.19-25.02-2.87-36.18-9.11-5.06-2.83-9.6 4.94-4.54 7.77h-.01Z"
            />
            <Path
              id="Vector_39"
              fill="#D66112"
              d="M448.58 1994.62c6.25 2.76 13.71 1.63 20.38 1.34 7.87-.35 15.72-.99 23.54-1.87 5.69-.64 5.76-9.64 0-9-6.57.74-13.16 1.31-19.77 1.68-3.15.17-6.29.3-9.44.39-2.86.08-7.49.87-10.17-.31-5.25-2.32-9.84 5.44-4.54 7.77Z"
            />
          </G>

          <G id="Group_3">
            {dataBtn.map((item, index) => (
              <BtnMission
                status={
                  data[index]?.numOfQuestion > 0 &&
                  data[index]?.numOfPass == data[index]?.numOfQuestion
                    ? 'complete'
                    : data[index]?.id
                      ? 'progress'
                      : 'inactive'
                }
                progress={data[index]?.numOfPass / data[index]?.numOfQuestion}
                key={item.id}
                x={item.x}
                y={item.y}
                title={`Mission ${index + 1}`}
                onItemPress={() =>
                  onItemPress(
                    data[index],
                    data[index]?.id ? data[index]?.id : -1,
                  )
                }
              />
            ))}
          </G>
        </G>
        <Defs>
          <ClipPath id="clip0_392_173">
            <Path fill="#fff" d="M0 0h1179v2556H0z" />
          </ClipPath>
        </Defs>
      </Svg>
    </View>
  );
};
export default ItemMission1;
