// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* CheppyTalkTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* CheppyTalkTests.m */; };
		0E66E562E8754339879A2D93 /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 0921BD9054834D74AD80CFF4 /* BootSplash.storyboard */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1EC28A2F62704B96AB1D1B22 /* SFRounded-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 29EC5447F80F4909BA1DB16F /* SFRounded-Medium.ttf */; };
		40F50FBE706B63D96EB4BDCB /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		582709B9816A4610B3A087BB /* SFRounded-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 1BC93465047B40E697F2CAC5 /* SFRounded-Regular.ttf */; };
		672E22069E79C4060FCC0110 /* libPods-CheppyTalk.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 56B5DEA45394DE4259D89C23 /* libPods-CheppyTalk.a */; };
		7010A07D2E0D0AF5006C9416 /* incorrect_answer.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7010A07A2E0D0AF5006C9416 /* incorrect_answer.mp3 */; };
		7010A07E2E0D0AF5006C9416 /* correct_answer.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7010A07B2E0D0AF5006C9416 /* correct_answer.mp3 */; };
		7010A07F2E0D0AF5006C9416 /* almost_correct_answer.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7010A07C2E0D0AF5006C9416 /* almost_correct_answer.mp3 */; };
		7068E8792E1CF710005EF046 /* you_are_not_enrolled_to_any_class.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7068E8782E1CF70F005EF046 /* you_are_not_enrolled_to_any_class.mp3 */; };
		7077A9142E1B5D1300C7EA6E /* a_snake_sneaks_to_seek_a_snack.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A90E2E1B5D1300C7EA6E /* a_snake_sneaks_to_seek_a_snack.mp3 */; };
		7077A9152E1B5D1300C7EA6E /* my_mate_is_at_the_gate.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A90F2E1B5D1300C7EA6E /* my_mate_is_at_the_gate.mp3 */; };
		7077A9162E1B5D1300C7EA6E /* lets_bake_a_cake.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A9102E1B5D1300C7EA6E /* lets_bake_a_cake.mp3 */; };
		7077A9172E1B5D1300C7EA6E /* she_sells_seashells_by_the_seashore.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A9112E1B5D1300C7EA6E /* she_sells_seashells_by_the_seashore.mp3 */; };
		7077A9182E1B5D1300C7EA6E /* sheep_on_the_ship.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A9122E1B5D1300C7EA6E /* sheep_on_the_ship.mp3 */; };
		7077A9192E1B5D1300C7EA6E /* the_bee_is_in_the_tree.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 7077A9132E1B5D1300C7EA6E /* the_bee_is_in_the_tree.mp3 */; };
		709B265A2E0D56F100F5898C /* tap_schoollist_myschool.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 709B26562E0D56F100F5898C /* tap_schoollist_myschool.mp3 */; };
		709B265B2E0D56F100F5898C /* wellcom_myschool.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 709B26572E0D56F100F5898C /* wellcom_myschool.mp3 */; };
		709B265C2E0D56F100F5898C /* enterclass_myschool.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 709B26582E0D56F100F5898C /* enterclass_myschool.mp3 */; };
		709B265D2E0D56F100F5898C /* tap_myschool.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 709B26592E0D56F100F5898C /* tap_myschool.mp3 */; };
		78EC0BB8DD368DEDFE984697 /* libPods-CheppyTalk-CheppyTalkTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5A6C57234E27C204E8E1253F /* libPods-CheppyTalk-CheppyTalkTests.a */; };
		848A23CD2E28ABFC00076617 /* cheppymussic.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 848A23CC2E28ABFC00076617 /* cheppymussic.mp3 */; };
		84971A352DF2FAC200172853 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 84971A342DF2FAC200172853 /* GoogleService-Info.plist */; };
		84D0264D2E2E32EC00FC2A20 /* levelpassed.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84D0264B2E2E32EC00FC2A20 /* levelpassed.mp3 */; };
		84D026512E2E3C9F00FC2A20 /* get_coin.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84D026502E2E3C9F00FC2A20 /* get_coin.mp3 */; };
		A4702265467D4C2EA4DE2FF1 /* SFRounded-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 729F8B84CCEF41E99B7636E7 /* SFRounded-SemiBold.ttf */; };
		AFC9E4CCE1C54DC586479617 /* SFRounded-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 16D8702672734390AB07F8F2 /* SFRounded-Bold.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = CheppyTalk;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* CheppyTalkTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CheppyTalkTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* CheppyTalkTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheppyTalkTests.m; sourceTree = "<group>"; };
		0921BD9054834D74AD80CFF4 /* BootSplash.storyboard */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = BootSplash.storyboard; path = CheppyTalk/BootSplash.storyboard; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* CheppyTalk.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CheppyTalk.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = CheppyTalk/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = CheppyTalk/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = CheppyTalk/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = CheppyTalk/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = CheppyTalk/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = CheppyTalk/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		149A4F5953F37923F4D770C8 /* Pods-CheppyTalk-CheppyTalkTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CheppyTalk-CheppyTalkTests.release.xcconfig"; path = "Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests.release.xcconfig"; sourceTree = "<group>"; };
		16D8702672734390AB07F8F2 /* SFRounded-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SFRounded-Bold.ttf"; path = "../assets/fonts/SFRounded-Bold.ttf"; sourceTree = "<group>"; };
		1BC93465047B40E697F2CAC5 /* SFRounded-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SFRounded-Regular.ttf"; path = "../assets/fonts/SFRounded-Regular.ttf"; sourceTree = "<group>"; };
		29EC5447F80F4909BA1DB16F /* SFRounded-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SFRounded-Medium.ttf"; path = "../assets/fonts/SFRounded-Medium.ttf"; sourceTree = "<group>"; };
		3C580973FAA1795841B32017 /* Pods-CheppyTalk.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CheppyTalk.release.xcconfig"; path = "Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk.release.xcconfig"; sourceTree = "<group>"; };
		56B5DEA45394DE4259D89C23 /* libPods-CheppyTalk.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-CheppyTalk.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		588D3B24057AC03A1EA52ED4 /* Pods-CheppyTalk-CheppyTalkTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CheppyTalk-CheppyTalkTests.debug.xcconfig"; path = "Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests.debug.xcconfig"; sourceTree = "<group>"; };
		5A6C57234E27C204E8E1253F /* libPods-CheppyTalk-CheppyTalkTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-CheppyTalk-CheppyTalkTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7010A07A2E0D0AF5006C9416 /* incorrect_answer.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = incorrect_answer.mp3; path = ../assets/sounds/incorrect_answer.mp3; sourceTree = "<group>"; };
		7010A07B2E0D0AF5006C9416 /* correct_answer.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = correct_answer.mp3; path = ../assets/sounds/correct_answer.mp3; sourceTree = "<group>"; };
		7010A07C2E0D0AF5006C9416 /* almost_correct_answer.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = almost_correct_answer.mp3; path = ../assets/sounds/almost_correct_answer.mp3; sourceTree = "<group>"; };
		7063B1882DF02184006213DA /* CheppyTalk.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = CheppyTalk.entitlements; path = CheppyTalk/CheppyTalk.entitlements; sourceTree = "<group>"; };
		7068E8782E1CF70F005EF046 /* you_are_not_enrolled_to_any_class.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = you_are_not_enrolled_to_any_class.mp3; path = ../assets/sounds/you_are_not_enrolled_to_any_class.mp3; sourceTree = "<group>"; };
		7077A90E2E1B5D1300C7EA6E /* a_snake_sneaks_to_seek_a_snack.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = a_snake_sneaks_to_seek_a_snack.mp3; path = ../assets/sounds/a_snake_sneaks_to_seek_a_snack.mp3; sourceTree = "<group>"; };
		7077A90F2E1B5D1300C7EA6E /* my_mate_is_at_the_gate.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = my_mate_is_at_the_gate.mp3; path = ../assets/sounds/my_mate_is_at_the_gate.mp3; sourceTree = "<group>"; };
		7077A9102E1B5D1300C7EA6E /* lets_bake_a_cake.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = lets_bake_a_cake.mp3; path = ../assets/sounds/lets_bake_a_cake.mp3; sourceTree = "<group>"; };
		7077A9112E1B5D1300C7EA6E /* she_sells_seashells_by_the_seashore.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = she_sells_seashells_by_the_seashore.mp3; path = ../assets/sounds/she_sells_seashells_by_the_seashore.mp3; sourceTree = "<group>"; };
		7077A9122E1B5D1300C7EA6E /* sheep_on_the_ship.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = sheep_on_the_ship.mp3; path = ../assets/sounds/sheep_on_the_ship.mp3; sourceTree = "<group>"; };
		7077A9132E1B5D1300C7EA6E /* the_bee_is_in_the_tree.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = the_bee_is_in_the_tree.mp3; path = ../assets/sounds/the_bee_is_in_the_tree.mp3; sourceTree = "<group>"; };
		709B26562E0D56F100F5898C /* tap_schoollist_myschool.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = tap_schoollist_myschool.mp3; path = ../assets/sounds/tap_schoollist_myschool.mp3; sourceTree = "<group>"; };
		709B26572E0D56F100F5898C /* wellcom_myschool.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = wellcom_myschool.mp3; path = ../assets/sounds/wellcom_myschool.mp3; sourceTree = "<group>"; };
		709B26582E0D56F100F5898C /* enterclass_myschool.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = enterclass_myschool.mp3; path = ../assets/sounds/enterclass_myschool.mp3; sourceTree = "<group>"; };
		709B26592E0D56F100F5898C /* tap_myschool.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = tap_myschool.mp3; path = ../assets/sounds/tap_myschool.mp3; sourceTree = "<group>"; };
		729F8B84CCEF41E99B7636E7 /* SFRounded-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SFRounded-SemiBold.ttf"; path = "../assets/fonts/SFRounded-SemiBold.ttf"; sourceTree = "<group>"; };
		848A23CC2E28ABFC00076617 /* cheppymussic.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = cheppymussic.mp3; sourceTree = "<group>"; };
		84971A342DF2FAC200172853 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		84D0264B2E2E32EC00FC2A20 /* levelpassed.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = levelpassed.mp3; sourceTree = "<group>"; };
		84D026502E2E3C9F00FC2A20 /* get_coin.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = get_coin.mp3; sourceTree = "<group>"; };
		E698C5D7A7753E84696729CE /* Pods-CheppyTalk.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CheppyTalk.debug.xcconfig"; path = "Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk.debug.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				78EC0BB8DD368DEDFE984697 /* libPods-CheppyTalk-CheppyTalkTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				672E22069E79C4060FCC0110 /* libPods-CheppyTalk.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* CheppyTalkTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* CheppyTalkTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = CheppyTalkTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* CheppyTalk */ = {
			isa = PBXGroup;
			children = (
				84971A342DF2FAC200172853 /* GoogleService-Info.plist */,
				7063B1882DF02184006213DA /* CheppyTalk.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				0921BD9054834D74AD80CFF4 /* BootSplash.storyboard */,
			);
			name = CheppyTalk;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				56B5DEA45394DE4259D89C23 /* libPods-CheppyTalk.a */,
				5A6C57234E27C204E8E1253F /* libPods-CheppyTalk-CheppyTalkTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* CheppyTalk */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* CheppyTalkTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				D6E843C3D75440A595B1438B /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* CheppyTalk.app */,
				00E356EE1AD99517003FC87E /* CheppyTalkTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				E698C5D7A7753E84696729CE /* Pods-CheppyTalk.debug.xcconfig */,
				3C580973FAA1795841B32017 /* Pods-CheppyTalk.release.xcconfig */,
				588D3B24057AC03A1EA52ED4 /* Pods-CheppyTalk-CheppyTalkTests.debug.xcconfig */,
				149A4F5953F37923F4D770C8 /* Pods-CheppyTalk-CheppyTalkTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D6E843C3D75440A595B1438B /* Resources */ = {
			isa = PBXGroup;
			children = (
				7077A90E2E1B5D1300C7EA6E /* a_snake_sneaks_to_seek_a_snack.mp3 */,
				7077A9102E1B5D1300C7EA6E /* lets_bake_a_cake.mp3 */,
				7077A90F2E1B5D1300C7EA6E /* my_mate_is_at_the_gate.mp3 */,
				7077A9112E1B5D1300C7EA6E /* she_sells_seashells_by_the_seashore.mp3 */,
				7077A9122E1B5D1300C7EA6E /* sheep_on_the_ship.mp3 */,
				7077A9132E1B5D1300C7EA6E /* the_bee_is_in_the_tree.mp3 */,
				709B26582E0D56F100F5898C /* enterclass_myschool.mp3 */,
				709B26592E0D56F100F5898C /* tap_myschool.mp3 */,
				7068E8782E1CF70F005EF046 /* you_are_not_enrolled_to_any_class.mp3 */,
				709B26562E0D56F100F5898C /* tap_schoollist_myschool.mp3 */,
				709B26572E0D56F100F5898C /* wellcom_myschool.mp3 */,
				7010A07C2E0D0AF5006C9416 /* almost_correct_answer.mp3 */,
				7010A07B2E0D0AF5006C9416 /* correct_answer.mp3 */,
				7010A07A2E0D0AF5006C9416 /* incorrect_answer.mp3 */,
				16D8702672734390AB07F8F2 /* SFRounded-Bold.ttf */,
				29EC5447F80F4909BA1DB16F /* SFRounded-Medium.ttf */,
				1BC93465047B40E697F2CAC5 /* SFRounded-Regular.ttf */,
				729F8B84CCEF41E99B7636E7 /* SFRounded-SemiBold.ttf */,
				848A23CC2E28ABFC00076617 /* cheppymussic.mp3 */,
				84D0264B2E2E32EC00FC2A20 /* levelpassed.mp3 */,
				84D026502E2E3C9F00FC2A20 /* get_coin.mp3 */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* CheppyTalkTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "CheppyTalkTests" */;
			buildPhases = (
				7E387732920DC69A856B794E /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				3672E9CA966DAF7D73718DF6 /* [CP] Embed Pods Frameworks */,
				74CBE0AE24F0A9452F6CA729 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = CheppyTalkTests;
			productName = CheppyTalkTests;
			productReference = 00E356EE1AD99517003FC87E /* CheppyTalkTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* CheppyTalk */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "CheppyTalk" */;
			buildPhases = (
				FB03C2ABF4DDD23B2FD49283 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				7712D8791C2995846FE8E659 /* [CP] Embed Pods Frameworks */,
				F8CEA167FCC6898842789AFA /* [CP] Copy Pods Resources */,
				9DEA634AE02275A23E7F2615 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CheppyTalk;
			productName = CheppyTalk;
			productReference = 13B07F961A680F5B00A75B9A /* CheppyTalk.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				KnownAssetTags = (
					New,
				);
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "CheppyTalk" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* CheppyTalk */,
				00E356ED1AD99517003FC87E /* CheppyTalkTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				709B265C2E0D56F100F5898C /* enterclass_myschool.mp3 in Resources */,
				7010A07E2E0D0AF5006C9416 /* correct_answer.mp3 in Resources */,
				84971A352DF2FAC200172853 /* GoogleService-Info.plist in Resources */,
				7010A07F2E0D0AF5006C9416 /* almost_correct_answer.mp3 in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				7077A9142E1B5D1300C7EA6E /* a_snake_sneaks_to_seek_a_snack.mp3 in Resources */,
				709B265B2E0D56F100F5898C /* wellcom_myschool.mp3 in Resources */,
				709B265D2E0D56F100F5898C /* tap_myschool.mp3 in Resources */,
				84D026512E2E3C9F00FC2A20 /* get_coin.mp3 in Resources */,
				7077A9162E1B5D1300C7EA6E /* lets_bake_a_cake.mp3 in Resources */,
				7077A9152E1B5D1300C7EA6E /* my_mate_is_at_the_gate.mp3 in Resources */,
				7077A9182E1B5D1300C7EA6E /* sheep_on_the_ship.mp3 in Resources */,
				7077A9192E1B5D1300C7EA6E /* the_bee_is_in_the_tree.mp3 in Resources */,
				848A23CD2E28ABFC00076617 /* cheppymussic.mp3 in Resources */,
				7010A07D2E0D0AF5006C9416 /* incorrect_answer.mp3 in Resources */,
				40F50FBE706B63D96EB4BDCB /* PrivacyInfo.xcprivacy in Resources */,
				84D0264D2E2E32EC00FC2A20 /* levelpassed.mp3 in Resources */,
				AFC9E4CCE1C54DC586479617 /* SFRounded-Bold.ttf in Resources */,
				1EC28A2F62704B96AB1D1B22 /* SFRounded-Medium.ttf in Resources */,
				582709B9816A4610B3A087BB /* SFRounded-Regular.ttf in Resources */,
				7068E8792E1CF710005EF046 /* you_are_not_enrolled_to_any_class.mp3 in Resources */,
				709B265A2E0D56F100F5898C /* tap_schoollist_myschool.mp3 in Resources */,
				7077A9172E1B5D1300C7EA6E /* she_sells_seashells_by_the_seashore.mp3 in Resources */,
				A4702265467D4C2EA4DE2FF1 /* SFRounded-SemiBold.ttf in Resources */,
				0E66E562E8754339879A2D93 /* BootSplash.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		3672E9CA966DAF7D73718DF6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		74CBE0AE24F0A9452F6CA729 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk-CheppyTalkTests/Pods-CheppyTalk-CheppyTalkTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7712D8791C2995846FE8E659 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7E387732920DC69A856B794E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CheppyTalk-CheppyTalkTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9DEA634AE02275A23E7F2615 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		F8CEA167FCC6898842789AFA /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CheppyTalk/Pods-CheppyTalk-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FB03C2ABF4DDD23B2FD49283 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CheppyTalk-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* CheppyTalkTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* CheppyTalk */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 588D3B24057AC03A1EA52ED4 /* Pods-CheppyTalk-CheppyTalkTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = CheppyTalkTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.ispeaking.demo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CheppyTalk.app/CheppyTalk";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 149A4F5953F37923F4D770C8 /* Pods-CheppyTalk-CheppyTalkTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = CheppyTalkTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.ispeaking.demo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CheppyTalk.app/CheppyTalk";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E698C5D7A7753E84696729CE /* Pods-CheppyTalk.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = CheppyTalk/CheppyTalk.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7DF3L5QB2X;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = CheppyTalk/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CheppyTalk;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.ispeaking.demo;
				PRODUCT_NAME = CheppyTalk;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3C580973FAA1795841B32017 /* Pods-CheppyTalk.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = CheppyTalk/CheppyTalk.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7DF3L5QB2X;
				INFOPLIST_FILE = CheppyTalk/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CheppyTalk;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.ispeaking.demo;
				PRODUCT_NAME = CheppyTalk;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "CheppyTalkTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "CheppyTalk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "CheppyTalk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
