import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {
  FlatList,
  ListRenderItem,
  Pressable,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import FastImage from 'react-native-fast-image';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import {CustomBottomSheet} from '../../components/BottomSheet.tsx';
import Button from '../../components/Button';
import Loading from '../../components/Loading.tsx';
import Spacer from '../../components/Spacer';
import TextApp from '../../components/TextApp';
import {useTheme} from '../../hooks/useTheme';
import {resetAndNavigate} from '../../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../../navigation/screenType.ts';
import {
  fetchChooseCharactor,
  fetchDataGrade,
  fetchDataInfo,
} from '../../redux/reducer/fetchData';
import {useReduxDispatch} from '../../redux/store.ts';
import {Theme} from '../../themes';
import {Images} from '../../themes/images';
import {FontFamily} from '../../themes/typography';
import {isEnglishOnly, isNullOrEmpty} from '../../utils';
import {
  heightScreen,
  isIOS,
  SCREEN_WIDTH,
  widthScreen,
} from '../../utils/Scale';
import Svg, {G, Image, Path, Text} from 'react-native-svg';

type GradeData = {
  id: number;
  name: string;
  modifiedDate: string;
  modifiedBy: string;
};

const characters = [
  {
    id: 1,
    label: 'Carp Boy',
    image: Images.boyAvt,
    x: 39.1,
    y: 205.8,
    textX: 107.022,
  },
  {
    id: 2,
    label: 'Carp Girl',
    image: Images.girlAvt,
    x: 200.489,
    y: 205.8,
    textX: 266.593,
  },
];

const dateFields = [
  {
    key: 'day',
    label: 'DD',
    xImage: 19.494,
    yImage: 647.965,
    xText: 69.215,
    yText: 670.965,
  },
  {
    key: 'month',
    label: 'MM',
    xImage: 139.494,
    yImage: 649.565,
    xText: 191.215,
    yText: 672.765,
  },
  {
    key: 'year',
    label: 'YY',
    xImage: 258.494,
    yImage: 649.565,
    xText: 309.215,
    yText: 672.765,
  },
];

export const ChooseCharacter = () => {
  const [name, setName] = useState<string>('');
  const [selectedCharacter, setSelectedCharacter] = useState<number>(1);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const [focused, setFocused] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [grade, setGrade] = useState<GradeData[]>([]);
  const [gradeSelected, setGradeSelected] = useState<GradeData>();
  const [showSheet, setShowSheet] = useState<boolean>(false);

  const theme = useTheme();
  const dispatch = useReduxDispatch();

  useEffect(() => {
    getGradeData();
  }, []);

  const getGradeData = async () => {
    const result = await dispatch(fetchDataGrade());
    if (fetchDataGrade.fulfilled.match(result) && result.payload?.data) {
      setGrade(result.payload.data);
    }
  };

  const openPicker = () => setIsPickerVisible(true);
  const onCancel = () => setIsPickerVisible(false);

  const onConfirm = (date: Date) => {
    setSelectedDate(date);
    setIsPickerVisible(false);
  };

  const onChangeInput = (value: string) => {
    setName(value);
  };

  const onFocus = () => {
    setFocused(true);
  };

  const onBlur = () => {
    setFocused(false);
    if (!isEnglishOnly(name)) {
      setError('Please use English characters only');
    } else {
      setError('');
    }
  };

  const handleOpenGrade = () => {
    setShowSheet(true);
  };

  const handleChooseGrade = (item: GradeData) => {
    setGradeSelected(item);
    setShowSheet(false);
  };

  const rederGradeItem: ListRenderItem<GradeData> = ({item}) => {
    const isSelected = gradeSelected?.id === item?.id;
    return (
      <Pressable
        style={[
          styles.gradeItem,
          isSelected && {
            backgroundColor: theme.bg_secondary_hover,
          },
        ]}
        onPress={() => handleChooseGrade(item)}>
        <TextApp text={item?.name} preset="text_md_regular" />
      </Pressable>
    );
  };

  const hanleCreatCharactor = async () => {
    setLoading(true);
    const params = {
      type: 'HOC-SINH',
      gradeId: gradeSelected?.id,
      genderId: selectedCharacter,
      dateOfBirth: moment(selectedDate).format('YYYY-MM-DD'),
      name: name,
    };
    const result = await dispatch(fetchChooseCharactor(params));
    if (fetchChooseCharactor.fulfilled.match(result)) {
      await dispatch(fetchDataInfo());
      setLoading(false);
      resetAndNavigate(APP_SCREEN.UN_AUTH);
    }
    if (fetchChooseCharactor.rejected.match(result)) {
      setLoading(false);
    }
  };

  const isDisabled =
    !name || isNullOrEmpty(gradeSelected) || !isNullOrEmpty(error);

  return (
    <KeyboardAwareScrollView
      testID="aware_scroll_view_container"
      showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <Svg
          width={'100%'}
          height={'100%'}
          viewBox="0 0 375 812"
          preserveAspectRatio="none"
          fill="none">
          <G id="" clip-path="url(#clip0_13_5335)">
            <G id="background">
              <Image
                x={0}
                y={0}
                id="image0_367_1053"
                width={375}
                height={812}
                preserveAspectRatio="xMidYMid slice"
                href={require('../../../assets/svg/chooseCharactor/bg_choose_charactor.png')}
              />
            </G>
            <G
              id="Choose Character Error Textbox Confirm"
              clip-path="url(#clip1_13_5335)">
              <G id="info">
                <G id="name">
                  <G id="Input with label">
                    <Text
                      x={187.215}
                      y={427.149}
                      fontWeight={'600'}
                      textAnchor="middle"
                      fontSize={18}
                      fontFamily={FontFamily.bold}
                      fill={'#FFFDD2'}>
                      Name
                    </Text>
                  </G>
                  <G id="Group_5">
                    <Image
                      x={32.494}
                      y={439.504}
                      id="image0_367_1053"
                      width={319}
                      height={40}
                      preserveAspectRatio="none"
                      href={require('../../../assets/svg/chooseCharactor/input.png')}
                    />
                  </G>
                </G>
                <G id="grade">
                  <G id="Input with label_2">
                    <G id="Label wrapper_2">
                      <Text
                        x={187.215}
                        y={536.765}
                        fontWeight={'600'}
                        textAnchor="middle"
                        fontSize={18}
                        fontFamily={FontFamily.bold}
                        fill={'#FFFDD2'}>
                        Grade
                      </Text>
                    </G>
                    <G id="Group 14331_2">
                      <Image
                        x={30.494}
                        y={550.765}
                        id="image0_367_1053"
                        width={319}
                        height={40}
                        preserveAspectRatio="none"
                        href={require('../../../assets/svg/chooseCharactor/input.png')}
                      />
                    </G>
                  </G>
                </G>
                <G id="date">
                  <G id="Frame 63_2">
                    <Text
                      x={187.215}
                      y={628.7}
                      fontWeight={'600'}
                      textAnchor="middle"
                      fontSize={18}
                      fontFamily={FontFamily.bold}
                      fill={'#FFFDD2'}>
                      Your birthday
                    </Text>
                  </G>
                  <G id="Frame 3359">
                    {dateFields.map((field, index) => (
                      <G key={index} id={`Group_${14332 + index}`}>
                        <Image
                          x={field.xImage}
                          y={field.yImage}
                          id={`image0_367_1053_${index}`}
                          width={100.86}
                          height={36.03}
                          preserveAspectRatio="none"
                          href={require('../../../assets/svg/chooseCharactor/input-date.png')}
                        />
                        <G id="Input field">
                          <Text
                            x={field.xText}
                            y={field.yText}
                            fontWeight="normal"
                            textAnchor="middle"
                            fontSize={16}
                            fontFamily={FontFamily.regular}
                            fill="#717680">
                            {field.key === 'day'
                              ? moment(selectedDate).format('DD')
                              : field.key === 'month'
                                ? moment(selectedDate).format('MMM')
                                : moment(selectedDate).format('YYYY')}
                          </Text>
                        </G>
                      </G>
                    ))}
                  </G>
                </G>
              </G>
              <Path
                id="Ant Design"
                fill="#FFFDD2"
                d="M57.898 117.149c-.703 0-1.125-.43-1.125-1.177v-3.973l-3.964-6.038a1.399 1.399 0 0 1-.228-.756c0-.598.483-1.037 1.124-1.037.475 0 .748.167 1.038.659l3.146 5.01h.053l3.155-5.01c.281-.474.563-.659 1.01-.659.634 0 1.117.439 1.117 1.028 0 .264-.07.501-.237.765l-3.955 6.038v3.973c0 .747-.422 1.177-1.134 1.177Zm9.36.036c-2.733 0-4.509-1.82-4.509-4.843 0-3.006 1.793-4.852 4.509-4.852s4.509 1.837 4.509 4.852c0 3.023-1.776 4.843-4.51 4.843Zm0-1.732c1.389 0 2.285-1.125 2.285-3.111 0-1.978-.896-3.112-2.285-3.112-1.38 0-2.285 1.134-2.285 3.112 0 1.986.896 3.111 2.285 3.111Zm9.536 1.714c-2.022 0-3.305-1.31-3.305-3.463v-5.054c0-.729.448-1.151 1.09-1.151.641 0 1.099.422 1.099 1.16v4.606c0 1.336.65 2.056 1.88 2.056 1.266 0 2.048-.896 2.048-2.25v-4.421c0-.729.457-1.151 1.099-1.151.633 0 1.09.422 1.09 1.16v7.409c0 .703-.422 1.108-1.046 1.108-.633 0-1.064-.405-1.064-1.108v-.685h-.044c-.492 1.134-1.432 1.784-2.847 1.784Zm8.243.009c-.659 0-1.09-.431-1.09-1.152v-7.418c0-.685.422-1.107 1.046-1.107.607 0 1.038.422 1.038 1.107v.712h.044c.254-1.089 1.063-1.793 1.995-1.793.343 0 .606.088.773.238.202.167.308.43.308.791 0 .342-.106.597-.325.773-.211.185-.537.281-.967.29-1.248.009-1.723.791-1.723 1.925v4.482c0 .721-.44 1.152-1.099 1.152Zm11.127 0c-.66 0-1.099-.405-1.099-1.143v-6.662h-.58c-.492 0-.844-.299-.844-.835 0-.51.352-.826.844-.826h.58v-.826c0-1.82.888-2.655 2.567-2.663 1.16 0 1.678.369 1.678.914 0 .334-.158.536-.483.633-.14.035-.29.052-.484.061-.8.018-1.125.369-1.125 1.151v.73h1.178c.483 0 .835.316.835.826 0 .536-.352.835-.835.835h-1.142v6.662c0 .738-.44 1.143-1.09 1.143Zm5.862-10.802a1.193 1.193 0 0 1-1.213-1.186c0-.668.536-1.187 1.213-1.187.685 0 1.221.519 1.221 1.187 0 .659-.536 1.186-1.221 1.186Zm0 10.802c-.668 0-1.09-.44-1.09-1.152v-7.374c0-.703.422-1.151 1.09-1.151.668 0 1.098.448 1.098 1.16v7.365c0 .712-.43 1.152-1.098 1.152Zm4.385 0c-.659 0-1.09-.431-1.09-1.152v-7.418c0-.685.422-1.107 1.046-1.107.607 0 1.037.422 1.037 1.107v.712h.044c.255-1.089 1.064-1.793 1.995-1.793.343 0 .607.088.774.238.202.167.307.43.307.791 0 .342-.105.597-.325.773-.211.185-.536.281-.967.29-1.248.009-1.722.791-1.722 1.925v4.482c0 .721-.44 1.152-1.099 1.152Zm9.052.009c-1.968 0-3.348-.756-3.796-1.873a1.162 1.162 0 0 1-.088-.43c0-.528.386-.861.914-.861.36 0 .668.167.949.536.492.712 1.055 1.028 2.101 1.028 1.019 0 1.696-.431 1.696-1.134 0-.562-.352-.87-1.31-1.09l-1.511-.342c-1.82-.414-2.716-1.31-2.716-2.664 0-1.731 1.494-2.865 3.797-2.865 1.793 0 3.181.879 3.471 1.951.027.115.044.22.044.326 0 .457-.29.782-.844.782-.316 0-.632-.114-.834-.404-.431-.642-.967-1.046-1.89-1.046-.958 0-1.617.465-1.617 1.133 0 .545.404.914 1.371 1.134l1.485.325c1.969.449 2.751 1.161 2.751 2.54 0 1.785-1.6 2.954-3.973 2.954Zm6.319-2.488v-5.326h-.588c-.545 0-.879-.325-.879-.852 0-.519.334-.835.879-.835h.588v-1.24c0-.703.422-1.142 1.099-1.142.668 0 1.09.439 1.09 1.142v1.24h1.011c.545 0 .879.316.879.835 0 .527-.334.852-.879.852h-1.011v4.852c0 .703.246 1.01.879 1.063l.158.018c.58.052.879.299.879.808 0 .642-.563.985-1.565.985h-.087c-1.626 0-2.453-.809-2.453-2.4Zm13.359 2.488c-1.969 0-3.349-.756-3.797-1.873a1.183 1.183 0 0 1-.088-.43c0-.528.387-.861.914-.861.361 0 .668.167.95.536.492.712 1.054 1.028 2.1 1.028 1.02 0 1.696-.431 1.696-1.134 0-.562-.351-.87-1.309-1.09l-1.512-.342c-1.819-.414-2.716-1.31-2.716-2.664 0-1.731 1.495-2.865 3.797-2.865 1.793 0 3.182.879 3.472 1.951.026.115.044.22.044.326 0 .457-.29.782-.844.782-.316 0-.633-.114-.835-.404-.431-.642-.967-1.046-1.889-1.046-.958 0-1.618.465-1.618 1.133 0 .545.405.914 1.371 1.134l1.486.325c1.969.449 2.751 1.161 2.751 2.54 0 1.785-1.6 2.954-3.973 2.954Zm6.319-2.488v-5.326h-.589c-.545 0-.879-.325-.879-.852 0-.519.334-.835.879-.835h.589v-1.24c0-.703.422-1.142 1.099-1.142.668 0 1.09.439 1.09 1.142v1.24h1.01c.545 0 .879.316.879.835 0 .527-.334.852-.879.852h-1.01v4.852c0 .703.246 1.01.878 1.063l.159.018c.58.052.879.299.879.808 0 .642-.563.985-1.565.985h-.088c-1.626 0-2.452-.809-2.452-2.4Zm9.764 2.488c-2.847 0-4.526-1.785-4.526-4.808 0-2.971 1.714-4.887 4.403-4.887 2.549 0 4.289 1.811 4.289 4.342 0 .65-.369 1.037-1.019 1.037h-5.493v.07c0 1.556.905 2.558 2.329 2.558.984 0 1.626-.343 2.329-1.239.228-.255.431-.352.738-.352.492 0 .879.317.879.844 0 .167-.053.36-.149.554-.616 1.186-2.004 1.881-3.78 1.881Zm-2.329-5.757h4.351c-.044-1.354-.905-2.241-2.136-2.241-1.23 0-2.127.905-2.215 2.241Zm9.36 8.885c-.641 0-1.09-.413-1.09-1.151v-10.538c0-.721.44-1.125 1.064-1.125.624 0 1.072.404 1.072 1.125v.668h.044c.527-1.081 1.582-1.767 2.936-1.767 2.364 0 3.867 1.837 3.867 4.817 0 2.97-1.494 4.807-3.832 4.807-1.354 0-2.408-.65-2.918-1.696h-.044v3.709c0 .738-.457 1.151-1.099 1.151Zm3.358-4.957c1.415 0 2.294-1.168 2.294-3.014 0-1.837-.879-3.015-2.294-3.015-1.363 0-2.268 1.204-2.277 3.015.009 1.828.914 3.014 2.277 3.014Zm11.407-8.982a1.193 1.193 0 0 1-1.213-1.186c0-.668.536-1.187 1.213-1.187.686 0 1.222.519 1.222 1.187 0 .659-.536 1.186-1.222 1.186Zm0 10.802c-.668 0-1.09-.44-1.09-1.152v-7.374c0-.703.422-1.151 1.09-1.151.668 0 1.099.448 1.099 1.16v7.365c0 .712-.431 1.152-1.099 1.152Zm4.386 0c-.642 0-1.09-.413-1.09-1.152v-7.418c0-.676.395-1.107 1.046-1.107.641 0 1.081.431 1.081 1.107v.651h.044c.501-1.108 1.467-1.749 2.9-1.749 2.057 0 3.226 1.318 3.226 3.471v5.045c0 .739-.449 1.152-1.09 1.152-.642 0-1.099-.413-1.099-1.152v-4.605c0-1.318-.615-2.057-1.863-2.057-1.257 0-2.057.906-2.057 2.242v4.42c0 .739-.457 1.152-1.098 1.152Zm9.931-2.479v-5.326h-.589c-.545 0-.879-.325-.879-.852 0-.519.334-.835.879-.835h.589v-1.24c0-.703.422-1.142 1.099-1.142.667 0 1.089.439 1.089 1.142v1.24h1.011c.545 0 .879.316.879.835 0 .527-.334.852-.879.852h-1.011v4.852c0 .703.246 1.01.879 1.063l.158.018c.581.052.879.299.879.808 0 .642-.562.985-1.564.985h-.088c-1.626 0-2.452-.809-2.452-2.4Zm9.747 2.488c-2.734 0-4.509-1.82-4.509-4.843 0-3.006 1.793-4.852 4.509-4.852 2.715 0 4.508 1.837 4.508 4.852 0 3.023-1.775 4.843-4.508 4.843Zm0-1.732c1.388 0 2.285-1.125 2.285-3.111 0-1.978-.897-3.112-2.285-3.112-1.38 0-2.286 1.134-2.286 3.112 0 1.986.897 3.111 2.286 3.111Zm10.845-.756v-5.326h-.589c-.545 0-.879-.325-.879-.852 0-.519.334-.835.879-.835h.589v-1.24c0-.703.421-1.142 1.098-1.142.668 0 1.09.439 1.09 1.142v1.24h1.011c.545 0 .879.316.879.835 0 .527-.334.852-.879.852h-1.011v4.852c0 .703.246 1.01.879 1.063l.158.018c.58.052.879.299.879.808 0 .642-.562.985-1.564.985h-.088c-1.626 0-2.452-.809-2.452-2.4Zm6.96 2.479c-.641 0-1.098-.413-1.098-1.152v-10.757c0-.695.413-1.125 1.081-1.125.676 0 1.081.43 1.081 1.125v4.016h.044c.501-1.134 1.529-1.775 2.944-1.775 2.057 0 3.261 1.353 3.261 3.489v5.027c0 .739-.457 1.152-1.099 1.152-.642 0-1.09-.413-1.09-1.152v-4.579c0-1.3-.65-2.083-1.916-2.083-1.318 0-2.118.914-2.118 2.285v4.377c0 .739-.448 1.152-1.09 1.152Zm13.57.009c-2.848 0-4.526-1.785-4.526-4.808 0-2.971 1.714-4.887 4.403-4.887 2.549 0 4.289 1.811 4.289 4.342 0 .65-.369 1.037-1.019 1.037h-5.494v.07c0 1.556.906 2.558 2.33 2.558.984 0 1.626-.343 2.329-1.239.228-.255.43-.352.738-.352.492 0 .879.317.879.844 0 .167-.053.36-.15.554-.615 1.186-2.003 1.881-3.779 1.881Zm-2.329-5.757h4.351c-.044-1.354-.906-2.241-2.136-2.241-1.231 0-2.127.905-2.215 2.241Zm13.007 5.721c-.694 0-1.142-.404-1.142-1.045 0-.176.044-.422.149-.695l3.683-10.011c.307-.843.817-1.23 1.643-1.23.835 0 1.345.369 1.661 1.222l3.692 10.019c.105.29.149.492.149.695 0 .615-.475 1.045-1.134 1.045-.615 0-.958-.281-1.16-.958l-.835-2.425h-4.737l-.835 2.408c-.211.685-.545.975-1.134.975Zm2.505-5.176h3.647l-1.801-5.414h-.062l-1.784 5.414Zm12.304 5.176c-2.346 0-3.867-1.854-3.867-4.816 0-2.944 1.521-4.808 3.867-4.808 1.336 0 2.391.695 2.883 1.723h.053v-3.955c0-.738.448-1.151 1.09-1.151.641 0 1.098.413 1.098 1.151v10.784c0 .703-.448 1.099-1.081 1.099-.632 0-1.107-.387-1.107-1.099v-.729h-.044c-.448 1.072-1.45 1.801-2.892 1.801Zm.66-1.793c1.379 0 2.285-1.168 2.285-3.014 0-1.828-.906-3.015-2.285-3.015-1.407 0-2.286 1.169-2.286 3.015 0 1.854.871 3.014 2.286 3.014Zm10.388 1.82c-.809 0-1.248-.325-1.538-1.116l-2.619-6.944a1.857 1.857 0 0 1-.123-.624c0-.597.465-.993 1.098-.993.519 0 .87.281 1.037.87l2.127 6.601h.044l2.127-6.618c.167-.581.519-.853 1.029-.853.615 0 1.063.422 1.063.984 0 .203-.044.413-.114.598l-2.619 6.979c-.273.764-.739 1.116-1.512 1.116Zm9.72.009c-2.847 0-4.526-1.785-4.526-4.808 0-2.971 1.714-4.887 4.403-4.887 2.549 0 4.289 1.811 4.289 4.342 0 .65-.369 1.037-1.019 1.037h-5.493v.07c0 1.556.905 2.558 2.329 2.558.984 0 1.626-.343 2.329-1.239.228-.255.43-.352.738-.352.492 0 .879.317.879.844 0 .167-.053.36-.149.554-.616 1.186-2.004 1.881-3.78 1.881Zm-2.329-5.757h4.351c-.044-1.354-.906-2.241-2.136-2.241-1.231 0-2.127.905-2.215 2.241Zm9.36 5.748c-.641 0-1.09-.413-1.09-1.152v-7.418c0-.676.396-1.107 1.046-1.107.642 0 1.081.431 1.081 1.107v.651h.044c.501-1.108 1.468-1.749 2.901-1.749 2.056 0 3.225 1.318 3.225 3.471v5.045c0 .739-.448 1.152-1.09 1.152-.641 0-1.098-.413-1.098-1.152v-4.605c0-1.318-.616-2.057-1.864-2.057-1.256 0-2.056.906-2.056 2.242v4.42c0 .739-.457 1.152-1.099 1.152Zm9.931-2.479v-5.326h-.588c-.545 0-.879-.325-.879-.852 0-.519.334-.835.879-.835h.588v-1.24c0-.703.422-1.142 1.099-1.142.668 0 1.09.439 1.09 1.142v1.24h1.011c.545 0 .879.316.879.835 0 .527-.334.852-.879.852h-1.011v4.852c0 .703.246 1.01.879 1.063l.158.018c.58.052.879.299.879.808 0 .642-.563.985-1.565.985h-.087c-1.626 0-2.453-.809-2.453-2.4Zm9.062 2.47c-2.022 0-3.305-1.31-3.305-3.463v-5.054c0-.729.448-1.151 1.09-1.151.641 0 1.098.422 1.098 1.16v4.606c0 1.336.651 2.056 1.881 2.056 1.266 0 2.048-.896 2.048-2.25v-4.421c0-.729.457-1.151 1.099-1.151.633 0 1.09.422 1.09 1.16v7.409c0 .703-.422 1.108-1.046 1.108-.633 0-1.064-.405-1.064-1.108v-.685h-.044c-.492 1.134-1.432 1.784-2.847 1.784Zm8.243.009c-.659 0-1.089-.431-1.089-1.152v-7.418c0-.685.421-1.107 1.045-1.107.607 0 1.038.422 1.038 1.107v.712h.043c.255-1.089 1.064-1.793 1.996-1.793.342 0 .606.088.773.238.202.167.308.43.308.791 0 .342-.106.597-.326.773-.21.185-.536.281-.966.29-1.248.009-1.723.791-1.723 1.925v4.482c0 .721-.439 1.152-1.099 1.152Zm9.246.009c-2.848 0-4.526-1.785-4.526-4.808 0-2.971 1.714-4.887 4.403-4.887 2.549 0 4.289 1.811 4.289 4.342 0 .65-.369 1.037-1.019 1.037h-5.494v.07c0 1.556.906 2.558 2.33 2.558.984 0 1.626-.343 2.329-1.239.228-.255.43-.352.738-.352.492 0 .879.317.879.844 0 .167-.053.36-.15.554-.615 1.186-2.003 1.881-3.779 1.881Zm-2.329-5.757h4.351c-.044-1.354-.906-2.241-2.136-2.241-1.231 0-2.127.905-2.215 2.241Z"
              />
            </G>
            <G id="Frame 3367">
              <Image
                x={10}
                y={695.399}
                id="image0_367_1053"
                width={357.04}
                height={70.6}
                preserveAspectRatio="none"
                href={require('../../../assets/svg/chooseCharactor/confirm.png')}
              />
            </G>
            <G id="charactor">
              {characters.map(char => {
                const isSelected = char.id === selectedCharacter;
                return (
                  <G
                    key={char.id}
                    id={char.label.replace(/\s/g, '').toLowerCase()}>
                    <Image
                      x={char.x}
                      y={isSelected ? char.y - 3 : char.y}
                      width={133.92}
                      height={isSelected ? 199.06 : 195.06}
                      preserveAspectRatio="none"
                      href={
                        isSelected
                          ? require('../../../assets/svg/chooseCharactor/charactor_selected.png')
                          : require('../../../assets/svg/chooseCharactor/charactor.png')
                      }
                    />
                    <Image
                      x={char.x + 10}
                      y={char.y + 30}
                      width={121}
                      height={145}
                      preserveAspectRatio="none"
                      href={char?.image}
                    />
                    <Text
                      x={char.textX}
                      y={isSelected ? char.y + 183.377 : char.y + 186.377}
                      fontWeight={isSelected ? 'bold' : 'normal'}
                      textAnchor="middle"
                      fontSize={14}
                      fontFamily={
                        isSelected ? FontFamily.bold : FontFamily.regular
                      }
                      fill="#6A2600">
                      {char.label}
                    </Text>
                  </G>
                );
              })}
            </G>
          </G>
        </Svg>
        <View style={styles.input}>
          <TextInput
            value={name}
            onChangeText={onChangeInput}
            placeholder="Use English characters only"
            placeholderTextColor={theme.text_placeholder}
            onFocus={onFocus}
            onBlur={onBlur}
            maxLength={20}
            style={{
              paddingVertical: 0,
              height: '100%',
              fontSize: 16,
              fontFamily: FontFamily.regular,
              textAlign: 'center',
            }}
          />
        </View>
        <TouchableOpacity style={styles.gradeBox} onPress={handleOpenGrade}>
          <View style={styles.gradeTextContainer}>
            <TextApp
              preset="text_md_regular"
              text={gradeSelected ? gradeSelected?.name : 'Choose your grade'}
              textColor={theme.text_placeholder}
            />
          </View>
          <SvgIcons.ArrowDown />
        </TouchableOpacity>
        {dateFields.map(field => (
          <Pressable
            key={field.key}
            onPress={openPicker}
            style={{
              position: 'absolute',
              width: scale(100),
              height: moderateVerticalScale(36),
              top: (field.yImage / 812) * heightScreen,
              left: (field.xImage / 375) * widthScreen,
              zIndex: 1,
            }}
          />
        ))}
        {characters.map(char => (
          <Pressable
            key={`pressable-${char.id}`}
            onPress={() => setSelectedCharacter(char.id)}
            style={{
              position: 'absolute',
              top: (char.y / 812) * heightScreen,
              left: (char.x / 375) * widthScreen,
              width: (133.92 / 375) * widthScreen,
              height: (195.06 / 812) * heightScreen,
              zIndex: 10,
            }}
          />
        ))}
      </View>
      <TouchableOpacity
        style={{
          height: 50,
          width: SCREEN_WIDTH - scale(55),
          position: 'absolute',
          top: (716.399 / 812) * heightScreen,
          left: (31.494 / 375) * widthScreen,
        }}
        onPress={hanleCreatCharactor}
        disabled={isDisabled}
      />
      {error && (
        <View style={styles.error}>
          <SvgIcons.Error />
          <TextApp
            text={error}
            preset="text_sm_regular"
            textColor={theme.text_error_primary}
            style={{marginLeft: scale(4)}}
          />
        </View>
      )}
      <DatePicker
        open={isPickerVisible}
        date={selectedDate}
        modal
        mode="date"
        onDateChange={setSelectedDate}
        onCancel={onCancel}
        onConfirm={onConfirm}
      />
      <CustomBottomSheet
        visible={showSheet}
        isOverlay
        onClose={() => setShowSheet(false)}
        containerStyle={{
          width: SCREEN_WIDTH,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          zIndex: 999,
        }}>
        <>
          <TextApp
            text={'Choose grade'}
            preset="text_lg_semibold"
            style={{
              textAlign: 'center',
              marginVertical: moderateVerticalScale(10),
            }}
          />
          <FlatList
            data={grade}
            keyExtractor={item => item?.id?.toString()}
            renderItem={rederGradeItem}
            scrollEnabled={false}
            keyboardShouldPersistTaps="handled"
          />
        </>
      </CustomBottomSheet>
      {loading && <Loading />}
    </KeyboardAwareScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2DD86',
    width: widthScreen,
    height: heightScreen,
  },
  titleText: {
    marginHorizontal: scale(20),
    textAlign: 'center',
    marginTop: scale(10),
  },
  characterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    gap: 32,
  },
  characterBox: {
    width: scale(112),
    height: scale(112),
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: Theme.radius.radius_xl,
    alignItems: 'center',
    padding: 10,
  },
  characterBoxSelected: {
    borderColor: '#F7941D',
    backgroundColor: '#FCF2E8',
    borderWidth: 2,
  },
  characterImage: {
    width: '100%',
    height: '100%',
  },
  characterLabel: {
    textAlign: 'center',
    marginTop: verticalScale(4),
  },
  input: {
    paddingHorizontal: scale(Theme.spacing.spacing_lg),
    height: 40,
    width: SCREEN_WIDTH - scale(55),
    position: 'absolute',
    top: (441.504 / 812) * heightScreen,
    left: (34.494 / 375) * widthScreen,
  },
  gradeBox: {
    paddingHorizontal: scale(Theme.spacing.spacing_lg),
    width: SCREEN_WIDTH - scale(55),
    height: 40,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: (552.504 / 812) * heightScreen,
    left: (32.494 / 375) * widthScreen,
  },
  gradeTextContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  birthdayRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: verticalScale(12),
    gap: 12,
  },
  birthdayItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateBox: {
    width: '100%',
    borderWidth: 1,
    borderColor: '#D5D7DA',
    borderRadius: Theme.radius.radius_md,
    paddingVertical: verticalScale(Theme.spacing.spacing_md),
    paddingHorizontal: scale(Theme.spacing.spacing_lg),
    backgroundColor: '#fff',
    marginTop: verticalScale(4),
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBtn: {
    width: SCREEN_WIDTH - scale(32),
    backgroundColor: '#F7941D',
    height: moderateVerticalScale(48),
  },
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 0,
    paddingBottom: isIOS ? verticalScale(30) : verticalScale(10),
  },
  error: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: scale(6),
    position: 'absolute',
    top: (472.504 / 812) * heightScreen,
    left: (32.494 / 375) * widthScreen,
  },
  gradeItem: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.spacing_lg,
    paddingVertical: Theme.spacing.spacing_md,
    marginHorizontal: scale(20),
  },
});
