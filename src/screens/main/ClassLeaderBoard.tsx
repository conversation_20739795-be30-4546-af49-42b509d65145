import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import Svg, {G, Image, Text} from 'react-native-svg';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import TextApp from '../../components/TextApp/index.tsx';
import {goBack, navigate} from '../../navigation/NavigationServices.ts';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType.ts';
import {useReduxDispatch, useTypedSelector} from '../../redux/store.ts';
import {Theme} from '../../themes/index.ts';
import {FontFamily} from '../../themes/typography.ts';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale.ts';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {fetchClassLeaderBoard} from '../../redux/reducer/fetchData.ts';

type rankItem = {
  id: string;
  name: string;
  exp: number;
  genderId: string;
  type: string;
  textX: number;
  textY: number;
  imageX: number;
  imageY: number;
  rank: string;
};

const awardData = [
  {
    type: 'award',
    textX: 158.927,
    textY: 170.99,
    imageX: 160.727,
    imageY: 183.99,
  },
  {
    type: 'award',
    textX: 257.927,
    textY: 185.99,
    imageX: 255.727,
    imageY: 195.99,
  },
  {
    type: 'award',
    textX: 70.927,
    textY: 195.99,
    imageX: 70.727,
    imageY: 205.99,
  },
];

const getAvatarImage = (genderId: string, rank: number) => {
  const g = genderId === '2' ? 'female' : 'male';
  const r = rank <= 3 ? rank : 1;
  switch (`${g}_${r}`) {
    case 'male_1':
      return require('../../../assets/svg/classLeaderBoard/avatar_male_rank1.png');
    case 'male_2':
      return require('../../../assets/svg/classLeaderBoard/avatar_male_rank2.png');
    case 'male_3':
      return require('../../../assets/svg/classLeaderBoard/avatar_male_rank3.png');
    case 'female_1':
      return require('../../../assets/svg/classLeaderBoard/avatar_female_rank1.png');
    case 'female_2':
      return require('../../../assets/svg/classLeaderBoard/avatar_female_rank2.png');
    case 'female_3':
      return require('../../../assets/svg/classLeaderBoard/avatar_female_rank3.png');
    default:
      return require('../../../assets/svg/classLeaderBoard/avatar_male_rank1.png');
  }
};

type ClassLeaderBoardProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.CLASS_LEADERBOARD
>;

const ClassLeaderBoard: React.FC<ClassLeaderBoardProps> = ({route}) => {
  const {classId} = route?.params;
  const {countNotification} = useTypedSelector(state => state.profile);
  const dispatch = useReduxDispatch();
  const [finalRankData, setFinalRankData] = useState<rankItem[]>([]);
  
  const handleNotification = () => {
    navigate(APP_SCREEN.NOTIFICATION);
  };

  useEffect(() => {
    const fetchData = async () => {
      const response = await dispatch(fetchClassLeaderBoard({classId}));
      const ranks = response?.payload?.data?.slice(0, 7) ?? [];
      const final = ranks.map((user: rankItem, index: number) => {
        const isAward = index < 3;
        return {
          ...user,
          type: isAward ? 'award' : 'rank',
          rank: index + 1,
          ...(isAward ? awardData[index] : {}),
        };
      });
      setFinalRankData(final);
    };

    fetchData();
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleNotification} hitSlop={HIT_SLOP}>
          <SvgIcons.Bell fill={'#fff'} />
          {countNotification > 0 && (
            <View style={styles.count}>
              <TextApp
                text={countNotification}
                preset="text_xs_medium"
                style={{lineHeight: 16}}
                textColor={'#fff'}
              />
            </View>
          )}
        </TouchableOpacity>
      </View>
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G id="Class Leaderboard" clip-path="url(#clip0_37_5353)">
          <G id="background">
            <G id="background">
              <Image
                x={0}
                y={0}
                id="image0_367_1053"
                width={375}
                height={812}
                preserveAspectRatio="xMidYMid slice"
                href={require('../../../assets/svg/classLeaderBoard/bg-class-leaderboard.webp')}
              />
            </G>
          </G>
          <G id="Frame 427320187">
            <G id="Component 14">
              <Image
                x={43.727}
                y={79.99}
                id="image0_367_1053"
                width={296}
                height={67.82}
                preserveAspectRatio="none"
                href={require('../../../assets/svg/classLeaderBoard/leaderboard-header.png')}
              />
            </G>
            <G id="Frame 427320200">
              {finalRankData
                .filter(item => item.type !== 'award')
                .map((item, index) => {
                  const baseY = 380.99 + index * 55;
                  return (
                    <G key={`rank-${index}`}>
                      <Image
                        x={41.927}
                        y={baseY}
                        width={300}
                        height={64}
                        preserveAspectRatio="none"
                        href={require('../../../assets/svg/classLeaderBoard/leaderboard-row-bg.png')}
                      />
                      <Image
                        x={41.927}
                        y={baseY}
                        width={64}
                        height={64}
                        preserveAspectRatio="none"
                        href={require('../../../assets/svg/classLeaderBoard/leaderboard-icon-rank.png')}
                      />
                      <Text
                        x={76.927}
                        y={baseY + 39}
                        fontWeight="bold"
                        textAnchor="middle"
                        fontSize={16}
                        fontFamily={FontFamily.bold}
                        fill="#974C00">
                        {item.rank}
                      </Text>
                      <Text
                        x={108.927}
                        y={baseY + 38}
                        fontWeight="bold"
                        textAnchor="start"
                        fontSize={16}
                        fontFamily={FontFamily.bold}
                        fill="#fff">
                        {item.name}
                      </Text>
                      <Text
                        x={323.927}
                        y={baseY + 38}
                        fontWeight="bold"
                        textAnchor="end"
                        fontSize={16}
                        fontFamily={FontFamily.bold}
                        fill="#fff">
                        {item.exp}
                      </Text>
                    </G>
                  );
                })}
            </G>
            <G id="Group_210">
              <Image
                x={30.727}
                y={230.99}
                id="image0_367_1053"
                width={324}
                height={146.56}
                preserveAspectRatio="none"
                href={require('../../../assets/svg/classLeaderBoard/award-podium.png')}
              />
              {finalRankData.map((item, index) => {
                if (item.type !== 'award') return null;
                return (
                  <G key={`award-${index}`} id={`nhan vat ${index + 1}`}>
                    <Text
                      x={item.textX + 32}
                      y={item.textY}
                      fontWeight="bold"
                      textAnchor="middle"
                      fontSize={14}
                      fontFamily={FontFamily.bold}
                      fill="#42210B">
                      {item.name}
                    </Text>
                    <Image
                      x={item.imageX}
                      y={item.imageY}
                      width={61.84}
                      height={99.32}
                      preserveAspectRatio="none"
                      href={getAvatarImage(item.genderId, index + 1)}
                    />
                  </G>
                );
              })}
            </G>
          </G>
        </G>
      </Svg>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FCF2E8',
    width: widthScreen,
    height: heightScreen,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },

  count: {
    width: 16,
    height: 16,
    backgroundColor: '#D92D20',
    borderRadius: 16,
    position: 'absolute',
    right: -2,
    top: -5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default ClassLeaderBoard;
