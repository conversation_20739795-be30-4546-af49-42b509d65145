import React, {useEffect, useState} from 'react';
import Header from '../../components/Header.tsx';
import SettingsItem from '../../components/SettingsItem.tsx';
import {Icons} from '../../themes/icons.ts';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import SettingsSection from '../../components/SettingsSection.tsx';
import {heightScreen, initTop, isAndroid} from '../../utils/Scale.ts';
import TextApp from '../../components/TextApp';
import {verticalScale} from 'react-native-size-matters';
import {resetAndNavigate} from '../../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../../navigation/screenType.ts';
import {fetchResetLogout} from '../../redux/reducer/fetchData.ts';
import store, {
  persistor,
  useReduxDispatch,
  useTypedSelector,
} from '../../redux/store.ts';
import useSound from '../../hooks/useSound.ts';
import {backgroundMusicManager} from '../../hooks/BackgroundMusicManager.ts';
import {AppConfig} from '../../config/index.tsx';

const Settings: React.FC = () => {
  const isSoundBg = useTypedSelector(state => state.profile.isPlayBgSound);
  const [autoPlay, setAutoPlay] = useState(true);
  const [haptic, setHaptic] = useState(true);
  const [enabled, setEnabled] = useState(
    backgroundMusicManager.isEnabledMusic(),
  );
  const dispatch = useReduxDispatch();
  const {stop} = useSound();
  const handleLogout = async () => {
    resetAndNavigate(APP_SCREEN.AUTH);
    try {
      const resultAction = await dispatch(fetchResetLogout());
      if (fetchResetLogout.fulfilled.match(resultAction)) {
        store.dispatch({type: 'RESET_STATE'});
        await persistor.purge();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // @ts-ignore
  useEffect(() => {
    const unsubscribe = backgroundMusicManager.subscribe(() => {
      setEnabled(backgroundMusicManager.isEnabledMusic());
    });

    return unsubscribe;
  }, []);

  const toggleMusic = (val: boolean) => {
    backgroundMusicManager.toggle(val); // cập nhật trạng thái thực
    setEnabled(val); // cập nhật UI ngay
  };
  return (
    <ImageBackground
      resizeMode={'stretch'}
      source={require('../../../assets/images/settings/bgsetting.webp')}
      style={{
        flex: 1,
        height: heightScreen + (isAndroid ? 0 : 130),
      }}>
      <Header title={''} />
      <TextApp
        text={'Settings'}
        preset={'text_xl_semibold'}
        textColor={'#395500'}
        style={{
          position: 'absolute',
          textAlign: 'center',
          width: '100%',
          top: isAndroid ? verticalScale(38) : initTop + 6,
        }}
      />
      <SafeAreaView style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <SettingsSection title={'Account'}>
            <SettingsItem
              label={'Edit profile'}
              icon={Icons.settings.editProfile}
            />
            <SettingsItem
              label={'Change password'}
              icon={Icons.settings.changePass}
            />
          </SettingsSection>
          <SettingsSection title={'General'}>
            <SettingsItem label={'Notification'} icon={Icons.settings.noti} />
            <SettingsItem
              label={'Auto play voice'}
              icon={Icons.settings.autoPlay}
              isSwitch={true}
              value={autoPlay}
              onValueChange={() => setAutoPlay(prevState => !prevState)}
            />
            <SettingsItem
              label={'Background music'}
              icon={Icons.settings.bgMussic}
              value={enabled}
              isSwitch={true}
              onValueChange={toggleMusic}
            />
            <SettingsItem
              label={'Haptic feedback'}
              value={haptic}
              icon={Icons.settings.haptic}
              isSwitch={true}
              onValueChange={() => setHaptic(prevState => !prevState)}
            />
          </SettingsSection>
          <SettingsSection title={'Parent Zone'}>
            <SettingsItem
              label={'Learning progress'}
              icon={Icons.settings.learning}
            />
            <SettingsItem label={'Set parent PIN'} icon={Icons.settings.pin} />
          </SettingsSection>
          <SettingsSection title={'Help Center'}>
            <SettingsItem label={'FAQs'} icon={Icons.settings.faqs} />
            <SettingsItem label={'Contact us'} icon={Icons.settings.phone} />
            <SettingsItem
              label={'Report a problem'}
              icon={Icons.settings.flag}
            />
          </SettingsSection>
          <View style={styles.boxRow}>
            <TouchableOpacity>
              <TextApp
                text={'Terms'}
                preset={'text_md_medium'}
                textColor={'#0086C9'}
                style={{textDecorationLine: 'underline'}}
              />
            </TouchableOpacity>
            <TextApp
              text={`${' '}and${' '}`}
              preset={'text_md_medium'}
              textColor={'#181D27'}
            />
            <TouchableOpacity>
              <TextApp
                text={'Policy'}
                preset={'text_md_medium'}
                textColor={'#0086C9'}
                style={{textDecorationLine: 'underline'}}
              />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={{alignSelf: 'center', marginTop: 12}}
            onPress={handleLogout}>
            <Image
              style={{
                width: 296,
                height: 56,
              }}
              source={require('../../../assets/images/settings/btnlogout.webp')}
              resizeMode={'contain'}
            />
          </TouchableOpacity>
          <TextApp
            text={`Version ${AppConfig.APP_VERSION}`}
            preset={'text_md_medium'}
            textColor={'#181D27'}
            style={{textAlign: 'center', marginTop: 12}}
          />
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 80,
  },
  boxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
});
export default Settings;
