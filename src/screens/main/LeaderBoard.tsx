import React from 'react';
import {StyleSheet, View} from 'react-native';
import Header from '../../components/Header.tsx';
import SvgCharacter from '../../../assets/svg/profile/SvgCharacter.tsx';

const LeaderBoard: React.FC = () => {
  return (
    <View style={styles.container}>
      <Header />
      {/*<Image*/}
      {/*  source={Images.helloman}*/}
      {/*  style={{width: 212, height: 318}}*/}
      {/*  resizeMode={'contain'}*/}
      {/*/>*/}
      {/*<TextApp*/}
      {/*  text={'Coming Soon: Smarter, Faster, Better English !'}*/}
      {/*  preset={'text_lg_bold'}*/}
      {/*  textColor={'#000000'}*/}
      {/*  style={{*/}
      {/*    width: 300,*/}
      {/*    textAlign: 'center',*/}
      {/*  }}*/}
      {/*/>*/}
      {/*<WellDone onPress={() => {}} answer={'17/17'} onPreview={() => {}} />*/}
      <SvgCharacter />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF2E8',
  },
});
export default LeaderBoard;
