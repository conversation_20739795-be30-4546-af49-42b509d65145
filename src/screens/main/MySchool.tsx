import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import SvgMySchool from '../../../assets/svg/myschool/MySchool';
import JoinClassModal from '../../components/JoinClassModal';
import JoinClassSuccessModal from '../../components/JoinClassSuccessModal';
import {SchoolList} from '../../components/SchoolList';
import useSound from '../../hooks/useSound';
import {goBack, navigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {useReduxDispatch} from '../../redux/store';
import {Theme} from '../../themes';
import {HIT_SLOP, initTop, isAndroid} from '../../utils/Scale';
import {fetchSchoolByStudent} from '../../redux/reducer/fetchData';

const initialClassButtons: any[] = [
  {
    id: 1,
    left: 745,
    top: 833,
    width: 244,
    height: 185,
    isOpen: false,
    type: 'top',
  },
  {
    id: 2,
    left: 454,
    top: 833,
    width: 244,
    height: 185,
    isOpen: false,
    type: 'top',
    className: '',
  },
  {
    id: 3,
    left: 152,
    top: 833,
    width: 244,
    height: 185,
    isOpen: false,
    type: 'top',
    className: '',
  },
  {
    id: 4,
    left: 105,
    top: 1190,
    width: 307,
    height: 285,
    isOpen: false,
    type: 'floor1',
  },
  {
    id: 5,
    left: 431,
    top: 1190,
    width: 290,
    height: 285,
    isOpen: false,
    type: 'floor1',
  },
  {
    id: 6,
    left: 745,
    top: 1215,
    width: 275,
    height: 255,
    type: 'fixed',
    source: require('../../../assets/svg/myschool/classroom-new.webp'),
  },
];

export const MySchool: React.FC = () => {
  const [listSchool, setListSchool] = useState([]);
  const [schoolSelected, setSchoolSelected] =
    useState<any[]>(initialClassButtons);
  const [schoolName, setSchoolName] = useState<string>('');
  const [visibleJoinClass, setVisibleJoinClass] = useState<boolean>(false);
  const [visibleJoinClassSuccess, setVisibleJoinClassSuccess] =
    useState<boolean>(false);
  const [contentBubble, setContentBubble] = useState<{
    content: string;
    sound: string;
  }>({
    content: 'Welcome to school\nMany exciting classes await!',
    sound: 'wellcom_myschool',
  });
  const [joinedClassId, setJoinedClassId] = useState<string | null>(null);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dispatch = useReduxDispatch();
  const {stop} = useSound();

  const cancelIdleTimeout = () => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
      idleTimeoutRef.current = null;
    }
  };

  const startIdleTimeout = (
    type: 'no_school' | 'one_school' | 'multiple_school',
  ) => {
    cancelIdleTimeout();
    idleTimeoutRef.current = setTimeout(() => {
      if (type === 'no_school') {
        setContentBubble({
          content: 'Tap the + button to join a new class!',
          sound: 'tap_myschool',
        });
      } else if (type === 'one_school') {
        setContentBubble({
          content: 'Enter your class to discover more!',
          sound: 'enterclass_myschool',
        });
      } else if (type === 'multiple_school') {
        setContentBubble({
          content: `Tap 'School list' to change another school!`,
          sound: 'tap_schoollist_myschool',
        });
      }
    }, 6000);
  };
  const fetchListSchool = async () => {
    const result = await dispatch(fetchSchoolByStudent());
    if (fetchSchoolByStudent.fulfilled.match(result)) {
      const schools = result?.payload?.data || [];
      setListSchool(schools);

      if (schools.length === 0) {
        startIdleTimeout('no_school');
      } else {
        const firstSchool = schools[0];
        callbackSelectSchool(firstSchool);
        if (schools.length === 1) {
          startIdleTimeout('one_school');
        } else {
          startIdleTimeout('multiple_school');
        }
      }
    }
  };
  const callbackSelectSchool = (data?: any) => {
    const openCount = data?.classes?.length || 0;
    setSchoolSelected(prevListClass =>
      prevListClass.map(item => {
        const shouldOpen =
          openCount >= 1 && item.id >= 5 - openCount + 1 && item.id <= 5;
        const classIndex = 5 - item.id;
        const matchedClass = data?.classes?.[classIndex];
        return {
          ...item,
          isOpen: shouldOpen,
          className: shouldOpen && matchedClass?.name ? matchedClass.name : '',
          info: matchedClass,
        };
      }),
    );
    setSchoolName(data?.schoolName || '');
    cancelIdleTimeout();
  };

  const handleClassDetail = async (item: any) => {
    if (item?.type === 'fixed') {
      cancelIdleTimeout();
      setVisibleJoinClass(true);
    } else {
      if (!item?.isOpen) return;
      navigate(APP_SCREEN.YOUR_CLASS, {
        classId: item?.info?.id,
        classStatus: item?.info?.classStatus,
      });
      cancelIdleTimeout();
    }
    stop();
  };

  const callbackWhenJoinClassSuccess = (classId: string) => {
    setJoinedClassId(classId);
    setVisibleJoinClass(false);
    setTimeout(() => {
      setVisibleJoinClassSuccess(true);
    }, 300);
  };

  useEffect(() => {
    fetchListSchool();
    return () => {
      cancelIdleTimeout();
    };
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={() => {
            cancelIdleTimeout();
            goBack();
          }}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <SchoolList
          options={listSchool}
          callbackSelectOption={callbackSelectSchool}
          callBackCreateNewSchool={() => setVisibleJoinClass(true)}
          callBackChangeModal={() => cancelIdleTimeout()}
        />
      </View>
      <SvgMySchool
        schoolName={schoolName}
        schoolSelected={schoolSelected}
        handleClassDetail={handleClassDetail}
        contentBubble={contentBubble}
      />
      <JoinClassModal
        visibleJoinClass={visibleJoinClass}
        closeJoinClass={() => setVisibleJoinClass(false)}
        callbackWhenJoinClassSuccess={callbackWhenJoinClassSuccess}
      />
      <JoinClassSuccessModal
        visible={visibleJoinClassSuccess}
        onClose={() => {
          setVisibleJoinClassSuccess(false);
          setJoinedClassId(null);
        }}
        classId={joinedClassId || ''}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2DD86',
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
});
