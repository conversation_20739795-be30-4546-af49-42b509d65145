import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Theme} from '../../themes';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale';
import {SvgIcons} from '../../../assets/svg';
import {goBack, navigate} from '../../navigation/NavigationServices';
import {scale, verticalScale} from 'react-native-size-matters';
import {APP_SCREEN} from '../../navigation/screenType';

export const PlayGround: React.FC = () => {
  const buttons = [
    {
      key: 'btn1',
      style: {
        position: 'absolute' as const,
        right: (20 / 375) * widthScreen,
        top: (195 / 812) * heightScreen,
        width: (180 / 375) * widthScreen,
        height: (150 / 812) * heightScreen,
      },
    },
    {
      key: 'btn2',
      style: {
        position: 'absolute' as const,
        left: (30 / 375) * widthScreen,
        top: (270 / 812) * heightScreen,
        width: (150 / 375) * widthScreen,
        height: (150 / 812) * heightScreen,
      },
    },
    {
      key: 'btn3',
      style: {
        position: 'absolute' as const,
        left: (60 / 375) * widthScreen,
        bottom: (220 / 812) * heightScreen,
        width: (180 / 375) * widthScreen,
        height: (170 / 812) * heightScreen,
      },
    },
    {
      key: 'btn4',
      style: {
        position: 'absolute' as const,
        right: (40 / 375) * widthScreen,
        bottom: (80 / 812) * heightScreen,
        width: (180 / 375) * widthScreen,
        height: (170 / 812) * heightScreen,
      },
    },
  ];

  const handlePress = (key: string) => {
    switch (key) {
      case 'btn1':
        navigate(APP_SCREEN.ROLE_PLAY);
        break;
      case 'btn4':
        navigate(APP_SCREEN.TOPIC_CARD);
        break;
      default:
        break;
    }
  };

  return (
    <View style={{flex: 1}}>
      <FastImage
        source={Theme.images.bgPlayGround}
        resizeMode="cover"
        style={{width: '100%', height: '100%', position: 'absolute'}}
      />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
      </View>
      <View style={{flex: 1}}>
        {buttons.map(btn => (
          <TouchableOpacity
            key={btn.key}
            style={btn.style}
            onPress={() => handlePress(btn.key)}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
});
