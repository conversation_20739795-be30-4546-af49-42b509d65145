import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  ListRenderItem,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {fetchUnitAssigned} from '../../redux/reducer/fetchData';
import InfoUnitModal from '../../components/modal/InfoUnitModal';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType';
import {goBack, navigate} from '../../navigation/NavigationServices';
import ItemMission1 from '../../../assets/svg/mission/ItemMission1';
import ItemMission2 from '../../../assets/svg/mission/ItemMission2';
import ItemMission3 from '../../../assets/svg/mission/ItemMission3';
import {LoadResource} from '../../components/LoadResource';
import {SvgIcons} from '../../../assets/svg';
import {HIT_SLOP, isAndroid} from '../../utils/Scale';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {Theme} from '../../themes';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ClassList, ClassListRef} from '../../components/ClassList';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import useSound from '../../hooks/useSound.ts';

type UnitProps = NativeStackScreenProps<RootStackParamList, APP_SCREEN.UNIT>;

type UnitItem = {
  id: string;
  name: string;
  [key: string]: any;
  isClock?: boolean;
};

type ChunkedGroup = {
  data: UnitItem[];
  Component: React.ComponentType<ChunkComponentProps>;
  groupIndex: number;
};

type ChunkComponentProps = {
  data: UnitItem[];
  active: number;
  onItemPress: (item: UnitItem, index: number) => void;
};

const GROUP_SIZES = [6, 7, 7];
const COMPONENTS = [ItemMission1, ItemMission2, ItemMission3];

const groupDataToChunks = (data: UnitItem[]): ChunkedGroup[] => {
  const result: ChunkedGroup[] = [];
  let currentIndex = 0;
  let groupIndex = 0;

  while (currentIndex <= data.length) {
    const size = GROUP_SIZES[groupIndex % GROUP_SIZES.length];
    const Component = COMPONENTS[groupIndex % COMPONENTS.length];
    const chunk = data.slice(currentIndex, currentIndex + size);
    result.push({data: chunk, Component, groupIndex});
    currentIndex += size;
    groupIndex++;
  }

  return result;
};

function getShuffledFileNames() {
  const fileNames = [
    'the_bee_is_in_the_tree',
    'my_mate_is_at_the_gate',
    'lets_bake_a_cake',
    'a_snake_sneaks_to_seek_a_snack',
    'she_sells_seashells_by_the_seashore',
    'sheep_on_the_ship',
  ];

  const randomIndex = Math.floor(Math.random() * fileNames.length);
  return fileNames[randomIndex];
}

function formatSentence(str: string) {
  if (!str) {
    return '';
  }
  const sentence = str.replace(/_/g, ' ');
  return sentence.charAt(0).toUpperCase() + sentence.slice(1);
}

const Unit: React.FC<UnitProps> = ({route}) => {
  const {classId} = route?.params ?? '';
  const dispatch = useReduxDispatch();
  const {dataAssigned, classByStudent} = useTypedSelector(state => state.unit);
  // const [page, setPage] = useState<number>(0);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [dataItem, setDataItem] = useState<UnitItem | null>(null);
  const [position, setPosition] = useState<number>(-1);
  const [loadUI, setLoadUI] = useState(false);
  const [keySound, setkeysound] = useState<string>('');
  const classRef = useRef<ClassListRef>(null);
  const hasFetchedByClassId = useRef(false);
  const {playLocalFile, stop} = useSound();
  const sound = getShuffledFileNames();
  useEffect(() => {
    if (classId && !hasFetchedByClassId.current) {
      hasFetchedByClassId.current = true;
      classRef?.current?.updateClassSelect(classId);
    }
  }, [classId, dispatch]);

  useEffect(() => {
    stop();
    playLocalFile(sound, false);
    setkeysound(sound);
    const timer = setTimeout(() => {
      setLoadUI(true);
    }, 3000);

    return () => {
      stop();
      clearTimeout(timer);
    };
  }, []);

  // const loadMore = () => {
  //   if (isLoadingMore) return;
  //   setIsLoadingMore(true);
  //   dispatch(fetchUnitAssigned({page: page + 1, size: 20})).finally(() => {
  //     setIsLoadingMore(false);
  //     setPage(prev => prev + 1);
  //   });
  // };

  const chunks = groupDataToChunks(dataAssigned);

  const handleItemPress = useCallback((item: UnitItem, index: number) => {
    setPosition(index);
    if (!item?.id) {
      setDataItem({name: 'currently not available', isClock: true} as UnitItem);
      setVisible(true);
    } else {
      setDataItem(item);
      setVisible(true);
    }
  }, []);

  const handleClose = () => {
    setVisible(false);
    setDataItem(null);
    setPosition(-1);
  };

  const handleConfirm = () => {
    if (dataItem?.id) {
      navigate(APP_SCREEN.LESSON, {unitId: dataItem.id});
    } else {
      setVisible(false);
    }
  };

  const renderChunk: ListRenderItem<ChunkedGroup> = ({item, index}) => {
    const {Component, data} = item;
    return (
      <Component
        key={index}
        active={position}
        data={data}
        onItemPress={(unit, idx) => handleItemPress(unit, idx)}
      />
    );
  };

  const callbackSelectClass = (item?: ClassData) => {
    dispatch(fetchUnitAssigned({classId: item?.id, page: 0, size: 60}));
  };

  return (
    <View style={styles.container}>
      {!loadUI && <LoadResource text={formatSentence(keySound)} />}
      <SafeAreaView edges={['top']} style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={() => {
            visible ? handleClose() : goBack();
          }}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <ClassList
          ref={classRef}
          options={classByStudent}
          callbackSelectOption={callbackSelectClass}
        />
      </SafeAreaView>
      <FlatList
        horizontal
        data={chunks}
        bounces={false}
        alwaysBounceVertical={false}
        renderItem={renderChunk}
        keyExtractor={(_, index) => `chunk-${index}`}
        // onEndReached={loadMore}
        onEndReachedThreshold={0.8}
        ListFooterComponent={isLoadingMore ? <ActivityIndicator /> : null}
        showsHorizontalScrollIndicator={false}
      />

      <InfoUnitModal
        isVisible={visible}
        onClose={handleClose}
        onStart={handleConfirm}
        title={dataItem?.name || ''}
        isClock={dataItem?.isClock}
        image={dataItem?.thumbnailPath}
        numberQuestion={dataItem?.numOfQuestion}
        numOfPass={dataItem?.numOfPass}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingTop: moderateVerticalScale(isAndroid ? 5 : 0),
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
});

export default Unit;
