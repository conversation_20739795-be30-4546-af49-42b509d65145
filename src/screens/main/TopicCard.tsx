import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import TextApp from '../../components/TextApp';
import {goBack} from '../../navigation/NavigationServices';
import {Theme} from '../../themes';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale';
import {TopicCardBackground} from '../../components/TopicCard/TopicCardBackground';
import {TopicCardList} from '../../components/TopicCard/TopicCardList';
import {useTopicCard} from '../../hooks/useTopicCard';
import CustomModal from '../../components/CustomModal';
import {TopicCardContentModal} from '../../components/TopicCard/TopicCardContentModal';
import {TopicCardMissionView} from '../../components/TopicCard/TopicCardMissionView';
import {TopicCardFinishModal} from '../../components/TopicCard/TopicCardFinishModal';
import {TopicCardBubble} from '../../components/TopicCard/TopicCardBubble';

export const TopicCard: React.FC = () => {
  const {
    modalVisible,
    topicCardData,
    modalContent,
    showMission,
    finishModal,
    handleCloseModal,
    handleStartNow,
    handleChooseCard,
    handleShowFinishModal,
    handleSeeFunTalk,
    handleCloseFinishModal,
  } = useTopicCard();
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <View style={styles.pear}>
          <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
          <TextApp
            text="450"
            preset="text_md_regular"
            textColor="#fff"
            style={{marginLeft: 4, lineHeight: 24}}
          />
        </View>
      </View>

      <TopicCardBackground />

      {showMission ? (
        <TopicCardMissionView handleMission={handleShowFinishModal} />
      ) : (
        <TopicCardList
          topicCardData={topicCardData}
          onChooseCard={handleChooseCard}
        />
      )}

      <CustomModal
        visible={modalVisible}
        children={
          <TopicCardContentModal
            modalContent={modalContent}
            onClose={handleCloseModal}
            onStartNow={handleStartNow}
          />
        }
      />
      <CustomModal
        visible={finishModal}
        children={
          <TopicCardFinishModal
            onSeeFullTalk={handleSeeFunTalk}
            onContinue={handleCloseFinishModal}
          />
        }
      />
      <TopicCardBubble />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  pear: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pearlIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
});
