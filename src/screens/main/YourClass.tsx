import {NativeStackScreenProps} from '@react-navigation/native-stack';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import SvgYourClass from '../../../assets/svg/yourClass/YourClass';
import TextApp from '../../components/TextApp';
import {goBack, navigate} from '../../navigation/NavigationServices';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType';
import {useReduxDispatch, useTypedSelector} from '../../redux/store';
import {Theme} from '../../themes';
import {HIT_SLOP, initTop, isAndroid} from '../../utils/Scale';
import {fetchClassDetail} from '../../redux/reducer/fetchData';
import FastImage from 'react-native-fast-image';
import SvgYouClassClosed from '../../../assets/svg/yourClass/SvgYouClassClosed';

type YourClassProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.YOUR_CLASS
>;

export const YourClass: React.FC<YourClassProps> = ({route}) => {
  const {classId, classStatus} = route?.params;
  const {countNotification} = useTypedSelector(state => state.profile);
  const [classInfor, setClassInfor] = useState<any>({});
  const dispatch = useReduxDispatch();

  useEffect(() => {
    const fetchClass = async () => {
      const result = await dispatch(fetchClassDetail({classId: classId}));
      setClassInfor(result?.payload?.data);
    };

    fetchClass();
  }, []);

  const handleNotification = () => {
    navigate(APP_SCREEN.NOTIFICATION);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleNotification} hitSlop={HIT_SLOP}>
          <SvgIcons.Bell fill={'#fff'} />
          {countNotification > 0 && (
            <View style={styles.count}>
              <TextApp
                text={countNotification}
                preset="text_xs_medium"
                style={{lineHeight: 16}}
                textColor={'#fff'}
              />
            </View>
          )}
        </TouchableOpacity>
      </View>
      {classStatus === 1 ? (
        <SvgYourClass
          className={classInfor?.name}
          teacher={classInfor?.teacherName}
          numberStudent={classInfor?.numberOfStudents}
          joinSince={moment(
            classInfor?.attemptTime,
            'DD/MM/YYYY HH:mm:ss',
          ).format('MMMM YYYY')}
          missionData={classInfor?.missons}
          classId={classId}
        />
      ) : (
        <SvgYouClassClosed
          className={classInfor?.name}
          teacher={classInfor?.teacherName}
          numberStudent={classInfor?.numberOfStudents}
          joinSince={moment(
            classInfor?.attemptTime,
            'DD/MM/YYYY HH:mm:ss',
          ).format('MMMM YYYY')}
          classId={classId}
        />
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2DD86',
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },

  count: {
    width: 16,
    height: 16,
    backgroundColor: '#D92D20',
    borderRadius: 16,
    position: 'absolute',
    right: -2,
    top: -5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
