import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale} from 'react-native-size-matters';
import {Theme} from '../themes';
import {CustomBottomSheet} from './BottomSheet';
import TextApp from './TextApp';
import IconClosed from '../../assets/svgIcons/IconClosed';
import {navigate} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';
import {useReduxDispatch} from '../redux/store';
import {fetchClassByStudent} from '../redux/reducer/fetchData';

const JoinClassSuccessModal = ({
  visible,
  onClose,
  classId,
}: {
  visible: boolean;
  onClose: () => void;
  classId: string;
}) => {
  const dispatch = useReduxDispatch();

  const classDetail = () => {
    dispatch(fetchClassByStudent({page: 0, size: 10}));
    onClose?.();
    setTimeout(() => {
      navigate(APP_SCREEN.YOUR_CLASS, {classId: classId});
    }, 200);
  };

  const studyNow = () => {
    dispatch(fetchClassByStudent({page: 0, size: 10}));
    onClose?.();
    setTimeout(() => {
      navigate(APP_SCREEN.UNIT, {classId: classId});
    }, 300);
  };

  return (
    <CustomBottomSheet
      visible={visible}
      isOverlay
      containerStyle={styles.container}>
      <View style={styles.background}>
        <View style={styles.characterContainer}>
          <FastImage
            source={Theme.images.charactorHappy}
            style={styles.character}
            resizeMode="contain"
          />
          {/* <View
            style={{
              width: 100,
              height: 100,
              backgroundColor: 'green',
              position: 'absolute',
              right: -20,
              top: -50,
            }}></View> */}
        </View>
        <TouchableOpacity style={styles.close} onPress={onClose}>
          <IconClosed />
        </TouchableOpacity>
        <View style={styles.modalContent}>
          <TextApp
            preset="display_xs_medium"
            text={'You are now in class!'}
            style={styles.title}
          />
          <TouchableOpacity style={styles.enterButton} onPress={studyNow}>
            <TextApp
              preset="text_sm_semibold"
              text={'Study now'}
              style={styles.enterButtonText}
              textColor="#fff"
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.closeButton} onPress={classDetail}>
            <TextApp
              preset="text_sm_semibold"
              text={'Class detail'}
              style={styles.closeButtonText}
              textColor="#414651"
            />
          </TouchableOpacity>
        </View>
      </View>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    bottom: 50,
    left: 50,
    right: 50,
    borderRadius: 16,
    paddingBottom: Theme.spacing.spacing_2xl,
    zIndex: 99,
  },
  background: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  characterContainer: {
    position: 'absolute',
    top: -165,
    zIndex: 1,
  },
  character: {
    width: 200,
    height: 200,
  },
  modalContent: {
    width: '100%',
    alignItems: 'center',
    marginTop: moderateVerticalScale(4),
  },
  title: {
    marginBottom: moderateVerticalScale(24),
    lineHeight: 28,
  },
  enterButton: {
    height: moderateVerticalScale(44),
    backgroundColor: '#ff9800',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 10,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  enterButtonText: {
    color: '#fff',
    lineHeight: 20,
  },
  closeButton: {
    height: moderateVerticalScale(44),
    borderRadius: 8,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  closeButtonText: {
    lineHeight: 20,
  },
  close: {
    alignSelf: 'flex-end',
  },
});

export default JoinClassSuccessModal;
