import React from 'react';
import {StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';
import {scale} from 'react-native-size-matters';
import IconCorrectItem from '../../../assets/svgIcons/IconCorrectItem.tsx';
import IconIncorrectItem from '../../../assets/svgIcons/IconIncorrectItem.tsx';
import {useTheme} from '../../hooks/useTheme.ts';
import {Theme} from '../../themes';
import TextApp from '../TextApp';
import {getImages} from '../../utils/getImages.ts';
import FastImage from 'react-native-fast-image';
import IconVolume from '../../../assets/svgIcons/IconVolume.tsx';

interface ItemQuestionProps {
  text?: string;
  isSelected?: boolean;
  isCorrect?: boolean;
  isWrong?: boolean;
  onPress?: () => void;
  image?: string;
  audio?: string;
  style?: ViewStyle;
  disable: boolean;
  isReview?: boolean;
}

const ItemQuestion: React.FC<ItemQuestionProps> = ({
  text,
  isSelected = false,
  isCorrect = false,
  isWrong = false,
  onPress,
  image,
  audio,
  style,
  disable,
  isReview = false,
}) => {
  const theme = useTheme();
  let containerStyle = styles.default;
  let borderColor = theme.fg_quaternary;
  let backgroundColor = 'transparent';
  let textColor = theme.fg_tertiary;
  let icon: React.ReactNode = null;

  if (isSelected) {
    borderColor = theme.fg_brand_secondary;
    backgroundColor = theme.bg_brand_secondary;
    textColor = theme.text_brand_secondary;
  }

  if (isCorrect) {
    borderColor = theme.fg_success_primary;
    backgroundColor = theme.bg_success_primary;
    textColor = theme.text_success_primary;

    icon = (
      <View style={styles.iconContainerGreen}>
        <IconCorrectItem />
      </View>
    );
  }

  if (isWrong) {
    borderColor = theme.fg_error_primary;
    backgroundColor = theme.bg_error_primary;
    textColor = theme.text_error_primary;

    icon = (
      <View style={styles.iconContainerRed}>
        <IconIncorrectItem />
      </View>
    );
  }
  if (isReview) {
    borderColor = theme.fg_success_primary;
    backgroundColor = theme.fg_success_primary;
    textColor = theme.text_white;
    icon = null;
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disable}
      activeOpacity={0.8}
      style={[styles.btn, {borderColor}, style]}>
      <View
        style={[
          (isWrong || isCorrect || isSelected) && {
            borderColor: theme.border_secondary,
          },
          styles.container,
          containerStyle,
          {backgroundColor},
        ]}>
        {icon}
        {text && (
          <TextApp
            text={text}
            preset="text_md_bold"
            textColor={textColor}
            style={{
              width: 130,
              textAlign: 'center',
              lineHeight: 24,
            }}
          />
        )}
        {image && (
          <FastImage
            source={getImages(image, true)}
            defaultSource={Theme.images.boyAvt}
            style={{
              width: '100%',
              height: '100%',
              flex: 1,
              borderRadius: Theme.radius.radius_lg,
            }}
          />
        )}
        {audio && !text && <IconVolume />}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  btn: {
    padding: 3,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderRadius: Theme.radius.radius_2xl,
    marginBottom: Theme.spacing.spacing_4xl,
  },
  container: {
    width: '100%',
    height: '100%',
    borderRadius: Theme.radius.radius_xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: scale(16),
    fontWeight: '600',
  },
  default: {},

  iconContainerGreen: {
    position: 'absolute',
    top: -18,
    left: -6,

    width: scale(24),
    height: scale(24),
    borderRadius: scale(12),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainerRed: {
    position: 'absolute',
    top: -18,
    left: -6,

    width: scale(24),
    height: scale(24),
    borderRadius: scale(12),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
});

export default ItemQuestion;
