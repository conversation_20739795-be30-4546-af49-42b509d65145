import React, {useEffect} from 'react';
import {Image, StyleSheet} from 'react-native';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import {Icons} from '../themes/icons.ts';
import useSound from '../hooks/useSound.ts';

type Props = {
  start: {x: number; y: number};
  end: {x: number; y: number};
  delay: number;
  onComplete: () => void;
};

const FlyGem: React.FC<Props> = ({start, end, delay, onComplete}) => {
  const x = useSharedValue(start.x);
  const y = useSharedValue(start.y);
  const opacity = useSharedValue(1);
  const {playLocalFile, playLocalFileWithHaptic} = useSound();
  useEffect(() => {
    playLocalFileWithHaptic('levelpassed');
    const time = setTimeout(() => {
      playLocalFile('get_coin', true);
      x.value = withDelay(delay, withTiming(end.x, {duration: 500}));
      y.value = withDelay(
        delay,
        withTiming(end.y, {duration: 500}, () => {
          runOnJS(onComplete)();
        }),
      );
      opacity.value = withDelay(delay + 200, withTiming(0));
    }, 2500);
    return () => clearTimeout(time);
  }, []);

  const rStyle = useAnimatedStyle(() => ({
    transform: [{translateX: x.value}, {translateY: y.value}],
    position: 'absolute',
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={[styles.container, rStyle]}>
      <Image source={Icons.gem} style={styles.gemIcon} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  gemIcon: {
    width: 24,
    height: 24,
  },
});

export default FlyGem;
