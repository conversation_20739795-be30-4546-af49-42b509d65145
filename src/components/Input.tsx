import React, {useState} from 'react';
import {
  StyleSheet,
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {
  moderateVerticalScale,
  ms,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../assets/svg';
import {useTheme} from '../hooks/useTheme';
import {Theme} from '../themes';
import {FontFamily} from '../themes/typography';
import TextApp from './TextApp';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  iconLeft?: any;
  isPassword?: boolean;
  inputStyle?: ViewStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  style,
  iconLeft,
  isPassword,
  inputStyle,
  ...props
}) => {
  const [show, setshow] = useState<boolean>(true);
  const theme = useTheme();
  if (isPassword) {
    return (
      <View style={styles.container}>
        {label && (
          <TextApp
            text={label}
            preset="text_sm_regular"
            style={styles.label}
            textColor={theme.text_secondary}
          />
        )}
        <View
          style={[
            styles.inputBox,
            {
              justifyContent: 'space-between',
            },
            inputStyle,
          ]}>
          <SvgIcons.Lock />
          <TextInput
            secureTextEntry={show}
            style={[styles.input, style, {color: theme.text_primary}]}
            placeholderTextColor={theme.text_placeholder}
            maxLength={16}
            {...props}
          />
          <TouchableOpacity onPress={() => setshow(!show)}>
            {show ? <SvgIcons.Eye /> : <SvgIcons.EyeOff />}
          </TouchableOpacity>
        </View>
        {error && (
          <View style={styles.error}>
            <SvgIcons.Error />
            <TextApp
              text={error}
              preset="text_sm_regular"
              textColor={theme.text_error_primary}
              style={{marginLeft: scale(4)}}
            />
          </View>
        )}
      </View>
    );
  }
  return (
    <View style={styles.container}>
      {label && (
        <TextApp
          preset="text_sm_regular"
          text={label}
          style={styles.label}
          textColor={theme.text_secondary}
        />
      )}
      <View style={[styles.inputBox, inputStyle]}>
        {iconLeft && iconLeft}
        <TextInput
          style={[styles.input, style, {color: theme.text_primary}]}
          placeholderTextColor={theme.text_placeholder}
          {...props}
        />
      </View>
      {error && (
        <View style={styles.error}>
          <SvgIcons.Error />
          <TextApp
            text={error}
            preset="text_sm_regular"
            textColor={theme.text_error_primary}
            style={{marginLeft: scale(4)}}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 12,
  },
  label: {
    marginBottom: verticalScale(6),
    lineHeight: ms(18),
  },
  input: {
    flex: 1,
    paddingVertical: 0,
    marginLeft: scale(8),
    height: '100%',
    marginRight: scale(4),
    fontFamily: FontFamily.regular,
    fontSize: ms(16),
  },
  error: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: scale(6),
  },
  inputBox: {
    borderWidth: 1,
    borderColor: Theme.colors.border,
    borderRadius: Theme.radius.radius_md,
    flexDirection: 'row',
    alignItems: 'center',
    height: moderateVerticalScale(44),
    paddingHorizontal: Theme.spacing.spacing_lg,
  },
});

export default Input;
