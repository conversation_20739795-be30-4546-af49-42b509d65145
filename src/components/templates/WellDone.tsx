import React, {useEffect, useRef, useState} from 'react';
import {
  findNodeHandle,
  Image,
  ImageBackground,
  StyleSheet,
  UIManager,
  View,
} from 'react-native';
import {Images} from '../../themes/images.ts';
import {Icons} from '../../themes/icons.ts';
import {Theme} from '../../themes';
import {heightScreen, isAndroid, widthScreen} from '../../utils/Scale.ts';
import TextApp from '../TextApp';
import ImageButton from '../ImageButton.tsx';
import GemDelta from '../GemDelta.tsx';
import FlyGem from '../FlyGem.tsx';
import <PERSON><PERSON><PERSON>ie<PERSON>iew from 'lottie-react-native';

interface WellDoneProps {
  onPress: () => void;
  answer: string;
  onPreview: () => void;
}

const NUM_GEMS = 10;
const DEVIATION: number = isAndroid ? 18 : 10;
const WellDone: React.FC<WellDoneProps> = ({onPress, answer, onPreview}) => {
  const [gemCount, setGemCount] = useState(10000);
  const [flyGems, setFlyGems] = useState<number[]>([]);
  const [showFromGemDelta, setShowFromGemDelta] = useState(false);
  const [gem, setGem] = useState(50);

  const fromRef = useRef<View>(null);
  const toRef = useRef<View>(null);
  const [start, setStart] = useState({x: 0, y: 0});
  const [end, setEnd] = useState({x: 0, y: 0});

  useEffect(() => {
    const timeout = setTimeout(() => {
      measureAndFly();
    }, 500);

    return () => clearTimeout(timeout);
  }, []);
  useEffect(() => {
    setShowFromGemDelta(true);
    const time = setTimeout(() => {
      setShowFromGemDelta(false);
    }, 3000);
    return () => clearTimeout(time);
  }, []);
  const measureAndFly = () => {
    if (!fromRef.current || !toRef.current) {
      return;
    }

    UIManager.measure(
      findNodeHandle(fromRef.current)!,
      (fx, fy, width, height, px, py) => {
        const from = {x: px + 72, y: py + DEVIATION};
        UIManager.measure(
          findNodeHandle(toRef.current)!,
          (fx2, fy2, width2, height2, px2, py2) => {
            const to = {x: px2 + 75, y: py2};
            setStart(from);
            setEnd(to);
            setFlyGems(Array.from({length: NUM_GEMS}, (_, i) => i));
          },
        );
      },
    );
  };

  const onGemComplete = () => {
    setGem(prev => prev - 5);
    setGemCount(prev => prev + 5);
  };
  return (
    <ImageBackground
      resizeMode="stretch"
      source={Images.bgComplete}
      style={styles.container}>
      <View ref={toRef} style={styles.gemBox}>
        <TextApp text={gemCount} preset="text_md_regular" textColor="#774900" />
        <Image source={Icons.gem} style={styles.gemIcon} />
        {showFromGemDelta && <GemDelta value={+50} color="#601700" />}
      </View>
      <Image
        resizeMode={'contain'}
        style={styles.imgComplete}
        source={Images.completeQ}
      />
      <View style={styles.infoRow}>
        <ImageBackground
          style={styles.infoBox}
          resizeMode="stretch"
          source={Images.frameText}>
          <TextApp text={answer} preset="display_md_bold" textColor="#F15A24" />
          <TextApp
            text="Correct Answers"
            preset="text_md_medium"
            textColor="#774900"
          />
        </ImageBackground>

        <ImageBackground
          style={[styles.infoBox, styles.mirrorBox]}
          resizeMode="stretch"
          source={Images.frameText}>
          <TextApp
            text="200"
            preset="display_md_bold"
            textColor="#F15A24"
            style={styles.mirrorText}
          />
          <TextApp
            text="EXP Earned"
            preset="text_md_medium"
            textColor="#774900"
            style={styles.mirrorText}
          />
        </ImageBackground>
      </View>

      <View
        ref={fromRef}
        style={[styles.rewardBox, {opacity: gem > 0 ? 1 : 0}]}>
        <TextApp
          text={`+${gem}`}
          preset="display_xs_bold"
          textColor="#601700"
        />
        <Image source={Icons.gem} style={styles.gemIcon} />
      </View>
      {flyGems.map((_, index) => (
        <FlyGem
          key={index}
          start={start}
          end={end}
          delay={index * 150}
          onComplete={() => onGemComplete()}
        />
      ))}

      <View style={styles.buttonRow}>
        <ImageButton
          onPress={onPreview}
          source={Images.btnReview}
          widthVal={157.5}
          title="Review"
        />
        <ImageButton
          onPress={onPress}
          source={Images.doAgain}
          widthVal={157.5}
          title="Continue"
        />
      </View>
      {showFromGemDelta && (
        <View style={styles.absoluteView}>
          <AnimatedLottieView
            source={require('../../../assets/lotties/congrats.json')}
            style={styles.lottie}
            autoPlay
            speed={0.7}
          />
        </View>
      )}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: widthScreen,
    paddingHorizontal: 16,
  },
  gemBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginTop: isAndroid ? 40 : 58,
    backgroundColor: 'rgba(138,217,255,0.85)',
    paddingHorizontal: 16,
    paddingVertical: 2,
    borderRadius: Theme.radius.radius_full,
  },
  gemIcon: {
    width: 24,
    height: 24,
    marginLeft: 2,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: isAndroid ? 330 : 380,
  },
  infoBox: {
    width: isAndroid ? 183 : 193,
    height: 91,
    opacity: 0.9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: isAndroid ? 16 : 0,
  },
  mirrorBox: {
    transform: [{scaleX: -1}],
  },
  mirrorText: {
    transform: [{scaleX: -1}],
  },
  rewardBox: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFAD00',
    borderRadius: Theme.radius.radius_full,
    borderWidth: 2,
    borderColor: '#FF8612',
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 4,
    marginTop: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 32,
  },
  imgComplete: {
    width: 274,
    height: 100,
    alignSelf: 'center',
    marginTop: isAndroid ? 40 : 80,
  },
  lottie: {
    width: widthScreen,
    height: heightScreen,
  },
  absoluteView: {
    ...StyleSheet.absoluteFillObject,
    width: widthScreen,
    height: '85%',
    top: 0,
  },
});

export default WellDone;
