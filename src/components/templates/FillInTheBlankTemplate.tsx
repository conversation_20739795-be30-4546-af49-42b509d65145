import React, {useEffect, useMemo, useState} from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import IconSpeak from '../../../assets/svgIcons/IconSpeak.tsx';
import useQuestion from '../../hooks/auth/useQuestion.ts';
import useSound from '../../hooks/useSound.ts';
import {useTheme} from '../../hooks/useTheme.ts';
import {Theme} from '../../themes';
import {getImages} from '../../utils/getImages.ts';
import {parseTextWithBlanks} from '../../utils/index.ts';
import ButtonFooter from '../ButtonFooter.tsx';
import {InputBlanks} from '../InputBlanks.tsx';
import TextApp from '../TextApp/index.tsx';
import useKeyboard from '../../hooks/useKeyBoard.ts';
import {isAndroid} from '../../utils/Scale.ts';
import {useTypedSelector} from '../../redux/store.ts';

interface FillInTheBlankTemplateProps {
  options: any[];
  title: string;
  question: string;
  answers: string[];
  audio: string;
  id: string;
  index: string | number;
  answerByStudent?: any;
  image: string;
  mediaType: 'AUDIO' | 'IMAGE';
  exerciseType: ExerciseType;
}

const FillInTheBlankTemplate: React.FC<FillInTheBlankTemplateProps> = ({
  options,
  title,
  question,
  id,
  index,
  answerByStudent,
  answers,
  audio,
  image,
  mediaType,
  exerciseType,
}) => {
  const theme = useTheme();
  const isDone: boolean = useTypedSelector(state => state.question.isDone);
  const {changeSource} = useSound();
  const {handleCheckAnswer, handleShowAnswer} = useQuestion();
  const {isKeyboardVisible} = useKeyboard({android: true});
  
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>(
    answerByStudent && answerByStudent.length
      ? answerByStudent
      : Array(options?.length || answers?.length).fill(''),
  );
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  useEffect(() => {
    if (answerByStudent && answerByStudent.length) {
      setIsSubmit(true);
    }
  }, [answerByStudent]);

  const parsedTextWithBlanks = useMemo(
    () => parseTextWithBlanks(question),
    [question],
  );

  const callbackInputBlank = (text: string, index: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[index] = text;
    setSelectedAnswers(newAnswers);
  };

  const isCorrectAnswer = () =>
    answers?.every(
      (item, i) => item.toLowerCase() === selectedAnswers[i]?.toLowerCase(),
    );

  const handleSubmit = () => {
    setIsSubmit(true);
    handleCheckAnswer(
      selectedAnswers,
      isCorrectAnswer(),
      id,
      index,
      exerciseType,
      answers,
      question,
    );
  };

  const isAllAnswered = options?.every((_, index) => !!selectedAnswers[index]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isDone ? moderateVerticalScale(isAndroid ? 0 : 16) : 0,
        },
      ]}>
      <KeyboardAvoidingView style={styles.scrollContainer} behavior={'padding'}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <ScrollView
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            scrollEnabled={isKeyboardVisible}>
            <TextApp
              text={title}
              preset="text_md_semibold"
              textColor={theme.text_secondary}
              style={{
                marginHorizontal: scale(25),
                textAlign: 'center',
                marginTop: isDone
                  ? moderateVerticalScale(145)
                  : moderateVerticalScale(135),
              }}
            />
            <View style={styles.shadowWrapper}>
              <TouchableOpacity
                activeOpacity={1}
                disabled={mediaType !== 'AUDIO'}
                style={styles.boxMedia}
                onPress={() => changeSource(audio || '')}>
                {mediaType === 'AUDIO' ? (
                  <IconSpeak />
                ) : (
                  <FastImage
                    resizeMode={'cover'}
                    source={getImages(image, true)}
                    defaultSource={Theme.images.boyAvt}
                    style={styles.image}
                  />
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.sentenceContainer}>
              {parsedTextWithBlanks.map((part, i) => {
                if (part.type === 'text') {
                  return (
                    <TextApp
                      key={`text-${id}-${index}-${i}`}
                      preset="text_sm_medium"
                      text={part?.value}
                      textColor={theme.text_secondary}
                    />
                  );
                }

                const inputIndex = parsedTextWithBlanks
                  .slice(0, i)
                  .filter(p => p.type === 'blank')?.length;

                return (
                  <InputBlanks
                    key={`blank-${id}-${index}-${i}`}
                    disable={!isSubmit}
                    value={selectedAnswers[inputIndex] || ''}
                    selectedAnswers={selectedAnswers}
                    answers={answers}
                    blankIndex={inputIndex}
                    isSubmit={isSubmit}
                    callbackInputBlank={callbackInputBlank}
                  />
                );
              })}
            </View>
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>

      {answerByStudent && !isCorrectAnswer() && (
        <ButtonFooter
          btnCheck={handleShowAnswer?.bind(
            null,
            answerByStudent,
            exerciseType,
            answers,
            question,
          )}
          title="Show the answer"
        />
      )}

      {!isDone && (
        <ButtonFooter
          btnCheck={handleSubmit}
          title="Submit"
          disabled={!isAllAnswered}
        />
      )}
      {isDone && (
        <View
          style={{
            height: 400,
            width: '100%',
            position: 'absolute',
            top: 360,
            zIndex: 30,
          }}
        />
      )}
    </View>
  );
};

export default FillInTheBlankTemplate;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flex: 1,
  },
  shadowWrapper: {
    shadowColor: '#0A0D121A',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 5,
    alignItems: 'center',
  },
  boxMedia: {
    width: scale(300),
    height: scale(200),
    backgroundColor: '#FFFFFF',
    borderRadius: Theme.radius.radius_2xl,
    marginBottom: moderateVerticalScale(25),
    marginTop: moderateVerticalScale(20),
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  sentenceContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginHorizontal: Theme.spacing.spacing_3xl,
    gap: 4,
  },
});
