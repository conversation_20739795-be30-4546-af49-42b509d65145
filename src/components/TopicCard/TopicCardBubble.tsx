import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import {Animated, StyleSheet, View} from 'react-native';
import {scale} from 'react-native-size-matters';
import {BubbleTail} from '../../../assets/svgIcons/BubbleTail';
import TextApp from '../TextApp';

export type TopicCardBubbleRef = {
  show: (content: string, isIdea?: boolean) => void;
  hide: () => void;
};

const MAX_BUBBLE_WIDTH = scale(212);
const BUBBLE_MARGIN = scale(20);

type TopicCardBubbleProps = {
  startIdleTimeout?: () => void;
  cancelIdleTimeout?: () => void;
};

const TopicCardBubbleComponent = forwardRef<
  TopicCardBubbleRef,
  TopicCardBubbleProps
>(({startIdleTimeout, cancelIdleTimeout}, ref) => {
  const [content, setContent] = useState<string>(
    'What is your favorite food for breakfast?',
  );
  const [key, setKey] = useState<number>(0);
  const [visible, setVisible] = useState<boolean>(false);
  const [isIdea, setIsIdea] = useState<boolean>(false);

  const scaleAnim = useState(new Animated.Value(0.5))[0];
  const opacityAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    //   const timeout = setTimeout(() => {
    setVisible(true);
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
    startIdleTimeout?.();
    //   }, 1500);
    return () => {
      //   clearTimeout(timeout);
      cancelIdleTimeout?.();
    };
  }, []);

  const showBubble = () => {
    setVisible(true);
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideBubble = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.5,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setVisible(false);
      setContent('');
    });
  };

  useImperativeHandle(ref, () => ({
    show(newContent: string, idea: boolean = false) {
      setContent(newContent);
      setIsIdea(idea);
      setKey(prev => prev + 1);
      showBubble();
    },
    hide() {
      hideBubble();
    },
  }));

  if (!visible) return null;

  return (
    <View style={[styles.container, isIdea && styles.containerIdea]}>
      <Animated.View
        style={[
          styles.bubble,
          {
            transform: [{scale: scaleAnim}],
            opacity: opacityAnim,
            width: MAX_BUBBLE_WIDTH,
          },
        ]}>
        <TextApp
          key={key}
          preset="text_md_medium"
          text={content}
          style={styles.content}
        />
        <View style={[styles.tailWrapper, isIdea && styles.tailWrapperIdea]}>
          <BubbleTail isIdea={isIdea} />
        </View>
      </Animated.View>
    </View>
  );
});

export const TopicCardBubble = React.memo(TopicCardBubbleComponent);

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: '15%',
    left: BUBBLE_MARGIN,
    right: BUBBLE_MARGIN,
    alignItems: 'center',
    zIndex: 90,
    justifyContent: 'center',
  },
  containerIdea: {
    bottom: '70%',
  },
  bubble: {
    backgroundColor: 'white',
    borderRadius: 8,
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowOffset: {width: 0, height: 4},
    shadowRadius: 8,
    elevation: 5,
    alignSelf: 'center',
    flexShrink: 1,
    flexGrow: 0,
  },
  content: {
    color: '#000',
    textAlign: 'center',
    lineHeight: scale(20),
    flexWrap: 'wrap',
    fontSize: scale(14),
  },

  tailWrapper: {
    position: 'absolute',
    bottom: scale(-12),
    alignSelf: 'center',
    transform: [{rotateY: '180deg'}],
  },
  tailWrapperIdea: {
    bottom: scale(-52),
    right: scale(60),
    transform: [{rotateY: '360deg'}],
  },
});
