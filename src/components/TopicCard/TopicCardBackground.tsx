import React from 'react';
import {View} from 'react-native';
import Svg, {G, Image} from 'react-native-svg';
import {Theme} from '../../themes';

export const TopicCardBackground = () => {
  return (
    <Svg
      width={'100%'}
      height={'100%'}
      viewBox="0 0 375 812"
      preserveAspectRatio="none"
      fill="none">
      <G>
        <G id="background">
          <Image
            x={0}
            y={0}
            id="image0_367_1053"
            width={375}
            height={812}
            preserveAspectRatio="xMidYMid slice"
            href={Theme.images.bgTopicCard}
          />
        </G>
      </G>
    </Svg>
  );
};
