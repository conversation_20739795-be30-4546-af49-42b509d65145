import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {RolePlayCard} from '../RolePlay/RolePlayCard';
import {Theme} from '../../themes';
import {heightScreen} from '../../utils/Scale';
import TextApp from '../TextApp';

interface TopicCardListProps {
  topicCardData: any[];
  onChooseCard: (item: any) => void;
  //   isLoading?: boolean;
  //   handleRandomCard: () => void;
}

export const TopicCardList: React.FC<TopicCardListProps> = ({
  topicCardData,
  onChooseCard,
  //   isLoading,
  //   handleRandomCard,
}) => {
  //   if (isLoading) {
  //     return (
  //       <View style={styles.loadingView}>
  //         <ActivityIndicator color={'#fff'} size={'large'} />
  //       </View>
  //     );
  //   }

  return (
    <View style={styles.container}>
      {topicCardData.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={styles.cardButton}
          onPress={onChooseCard?.bind(null, item)}>
          <TextApp text={`Card ${index + 1}`} style={styles.cardButtonText} />
        </TouchableOpacity>
      ))}
      {/* <TouchableOpacity style={styles.extraCard} onPress={handleRandomCard}>
        <FastImage
          source={Theme.images.rolePlayCardCharactor5}
          style={styles.extraCardImage}
          resizeMode="contain"
        />
      </TouchableOpacity> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: scale(13),
    justifyContent: 'center',
    position: 'absolute',
    alignSelf: 'center',
    top: (463 / 813) * heightScreen,
  },
  loadingView: {
    position: 'absolute',
    bottom: (120 / 813) * heightScreen,
    alignSelf: 'center',
  },
  extraCard: {
    width: 80,
    height: 100,
  },
  extraCardImage: {
    width: '100%',
    height: '100%',
  },
  cardButton: {
    backgroundColor: '#FFA500',
    width: scale(60),
    height: moderateVerticalScale(100),
    borderRadius: 12,
    margin: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardButtonText: {
    fontSize: moderateVerticalScale(14),
    color: 'white',
    fontWeight: 'bold',
  },
});
