import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Button from '../Button';

interface TopicCardContentModalProps {
  modalContent: any | null;
  onClose: () => void;
  onStartNow: () => void;
}

export const TopicCardContentModal: React.FC<TopicCardContentModalProps> = ({
  modalContent,
  onClose,
  onStartNow,
}) => {
  return (
    <View style={styles.outerContainer}>
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Text>X</Text>
      </TouchableOpacity>
      <View style={styles.innerContainer}>
        <Text style={styles.titleText}>{modalContent?.title}</Text>
        {/* <Image
          source={modalContent?.image}
          style={styles.image}
          resizeMode="cover"
        /> */}
        <Text style={styles.questionText}>{modalContent?.question}</Text>
        <Text style={styles.missionTextModal}>{modalContent?.mission}</Text>
        <View style={styles.rewardContainer}>
          <Text style={styles.rewardText}>{modalContent?.reward}</Text>
        </View>
        <Button
          title="Start Now"
          onPress={onStartNow}
          style={styles.startButton}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    backgroundColor: '#FFEECD',
    borderRadius: 19,
    padding: 9,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: -10,
    top: -10,
    width: 25,
    height: 25,
    backgroundColor: '#D9D9D9',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    backgroundColor: '#fff',
    borderWidth: 3,
    borderColor: '#F99A3D',
    borderRadius: 19,
    paddingHorizontal: 15,
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FF7E00',
  },
  questionText: {
    fontSize: 18,
    fontWeight: '700',
  },
  missionTextModal: {
    fontSize: 12,
    fontWeight: '400',
    fontStyle: 'italic',
    lineHeight: 22,
    marginVertical: 10,
    textAlign: 'center',
  },
  rewardContainer: {
    backgroundColor: '#D9D9D9',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 7,
    marginTop: 5,
  },
  rewardText: {
    fontSize: 12,
    fontWeight: '400',
    fontStyle: 'italic',
  },
  startButton: {
    backgroundColor: '#F99A3D',
    width: '80%',
    marginTop: 10,
  },
});
