import React, {memo, useMemo} from 'react';
import {SharedValue, useDerivedValue} from 'react-native-reanimated';
import {Canvas, Image, useImage, Group} from '@shopify/react-native-skia';
import {Theme} from '../../themes';
import {heightScreen, widthScreen} from '../../utils/Scale';

interface RolePlayBackgroundProps {
  modalContent: RolePlayCard | null;
  trasnY: SharedValue<number>;
  opacity: SharedValue<number>;
}

export const RolePlayBackground: React.FC<RolePlayBackgroundProps> = memo(
  ({modalContent, trasnY, opacity}) => {
    const blurImage = useImage(Theme.images.rolePlayBlur);
    const nameImage = useImage(Theme.images.rolePlayName);
    const backgroundImage = useImage(Theme.images.bgRolePlay);

    const characterImageSource = useMemo(
      () =>
        (Theme.images as any)[modalContent?.rlFigureCode || ''] ??
        Theme.images.rolePlayCharactorDefault,
      [modalContent?.rlFigureCode],
    );

    const characterImage = useImage(characterImageSource);

    const nameTransform = useDerivedValue(
      () => [{translateY: trasnY.value}],
      [trasnY],
    );

    const characterTransform = useDerivedValue(
      () => [
        {translateX: (115.51 / 375) * widthScreen},
        {translateY: (310.3 / 812) * heightScreen},
      ],
      [],
    );

    if (!blurImage || !nameImage || !backgroundImage || !characterImage) {
      return null;
    }

    return (
      <Canvas style={{width: widthScreen, height: heightScreen}}>
        <Image
          image={blurImage}
          x={0}
          y={0}
          width={widthScreen}
          height={heightScreen}
          fit="fill"
          opacity={0.8}
        />
        <Group transform={nameTransform}>
          <Image
            image={nameImage}
            x={0}
            y={0}
            width={widthScreen}
            height={(248.16 / 812) * heightScreen}
            fit="fill"
          />
        </Group>
        <Image
          image={backgroundImage}
          x={0}
          y={0}
          width={widthScreen}
          height={heightScreen}
          fit="cover"
        />

        <Group transform={characterTransform} opacity={opacity}>
          <Image
            image={characterImage}
            x={0}
            y={0}
            width={(143.97 / 375) * widthScreen}
            height={(236.7 / 812) * heightScreen}
            fit="fill"
          />
        </Group>
      </Canvas>
    );
  },
);
