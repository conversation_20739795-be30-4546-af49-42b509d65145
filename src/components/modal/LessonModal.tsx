import React, {useEffect, useState} from 'react';
import {
  Image,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {
  cancelAnimation,
  Easing,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';

import {SCREEN_WIDTH, widthScreen} from '../../utils/Scale';
import {Images} from '../../themes/images';
import TextApp from '../TextApp';
import QuesionPopupLessonIcon from '../../../assets/svgIcons/QuesionPopupLessonIcon.tsx';
import TimeLessonIcon from '../../../assets/svgIcons/TimeLessonIcon.tsx';
import AttemptIcon from '../../../assets/svgIcons/AttemptIcon.tsx';
import TimeLimitLessonIcon from '../../../assets/svgIcons/TimeLimitLessonIcon.tsx';
import ImageButton from '../ImageButton.tsx';

const SIZE = 600;

interface PopupModalProps {
  isVisible: boolean;
  onClose: () => void;
  onReview: () => void;
  onRedo: () => void;
  onStart: () => void;
  title: string;
  content?: string;
  numberQuestion: string | number;
  time: string | number;
  isDone: boolean;
  attemptLimit?: string;
  isClock?: boolean;
  numberOfAttempt?: number;
  numberOfDaysLeft?: number;
}

const LessonModal: React.FC<PopupModalProps> = ({
  isVisible,
  onClose,
  onReview,
  onStart,
  onRedo,
  title,
  numberQuestion,
  time,
  isDone,
  isClock,

  numberOfAttempt,
  numberOfDaysLeft,
}) => {
  const rotation = useSharedValue(0);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const loop = () => {
      if (!isMounted) {
        return;
      }
      rotation.value = withTiming(
        rotation.value + 360,
        {
          duration: 15000,
          easing: Easing.linear,
        },
        finished => {
          if (finished && isMounted) {
            runOnJS(loop)();
          }
        },
      );
    };

    loop();

    return () => {
      isMounted = false;
      cancelAnimation(rotation);
      setImageLoaded(false);
    };
  }, [rotation]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{rotate: `${rotation.value % 360}deg`}],
  }));

  if (!isVisible) {
    return null;
  }

  const ActionButton = ({
    onPress,
    source,
    widthVal,
  }: {
    onPress: () => void;
    source: any;
    widthVal: number;
    disable?: boolean;
  }) => (
    <TouchableOpacity onPress={onPress}>
      <Image
        source={source}
        resizeMode="contain"
        style={[styles.button, {width: scale(widthVal)}]}
      />
    </TouchableOpacity>
  );

  return (
    <View style={styles.overlay}>
      <Pressable style={styles.backdrop} onPress={onClose} />

      <View style={styles.modalContent}>
        <Animated.View style={[styles.imageContainer, animatedStyle]}>
          {imageLoaded && (
            <Image
              source={Images.bgLight}
              style={styles.image}
              resizeMode="contain"
            />
          )}
        </Animated.View>

        <View style={styles.absoluteCenter}>
          <Image
            source={Images.bgPopupLesson}
            resizeMode="contain"
            onLoad={() => setImageLoaded(true)}
            style={styles.popupImage}
          />

          <View style={styles.textContainer}>
            <TextApp
              text={title}
              preset="text_lg_semibold"
              textColor="#4C321F"
            />
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginTop: 12,
                paddingHorizontal: 8,
              }}>
              <View style={styles.statsRow}>
                <QuesionPopupLessonIcon />
                <TextApp
                  text={`${numberQuestion} questions`}
                  preset="text_xs_regular"
                  textColor="#4C321F"
                  style={{marginLeft: 3}}
                />
              </View>
              <View style={styles.statsRow}>
                <TimeLessonIcon />
                <TextApp
                  text={`${time} mins`}
                  preset="text_xs_regular"
                  textColor="#4C321F"
                  style={{marginLeft: 3, width: 66}}
                />
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginTop: 10,
                paddingHorizontal: 8,
              }}>
              <View style={styles.statsRow}>
                <AttemptIcon width={20} height={20} />
                <TextApp
                  text={`${numberOfAttempt} attempt`}
                  preset="text_xs_regular"
                  textColor="#4C321F"
                  style={{marginLeft: 3}}
                />
              </View>
              <View style={styles.statsRow}>
                <TimeLimitLessonIcon />
                <TextApp
                  text={`${numberOfDaysLeft > 0 ? numberOfDaysLeft : 0} days left`}
                  preset="text_xs_regular"
                  textColor="#4C321F"
                  style={{marginLeft: 3, width: 66}}
                />
              </View>
            </View>
          </View>
        </View>

        <View style={styles.buttonRow}>
          {isDone ? (
            <>
              <ImageButton
                onPress={onReview}
                source={Images.btnReview}
                widthVal={157.5}
                title={'Preview'}
              />
              <ImageButton
                onPress={onRedo}
                source={Images.doAgain}
                widthVal={157.5}
                title={'Do again'}
              />
            </>
          ) : (
            <ActionButton
              onPress={isClock ? onClose : onStart}
              source={isClock ? Images.btnClockLesson : Images.btnActiveLesson}
              widthVal={327}
            />
          )}
        </View>
      </View>
    </View>
  );
};

export default LessonModal;

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 99,
    backgroundColor: 'rgba(10, 13, 18, 0.4)',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContent: {
    width: SCREEN_WIDTH,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: SIZE,
    height: SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: SIZE,
    height: SIZE,
  },
  absoluteCenter: {
    width: widthScreen,
    position: 'absolute',
    alignItems: 'center',
  },
  popupImage: {
    width: scale(306),
    height: verticalScale(286),
  },
  textContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: moderateVerticalScale(110),
    width: '50%',
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -80,
    justifyContent: 'space-between',
    width: widthScreen,
    paddingHorizontal: 16,
  },
  button: {
    height: verticalScale(48),
  },
});
