import React, {useState} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {Images} from '../themes/images.ts';
import TextApp from './TextApp';
import {Icons} from '../themes/icons.ts';
import {isAndroid} from '../utils/Scale.ts';
import Wardrobe from './Wardrobe.tsx';
import {Theme} from '../themes';

type TabType = 'Progresses' | 'Archievements' | 'Wardrobe';

const ButtonTab = ({
  isActive,
  text,
  onPress,
}: {
  isActive: boolean;
  text: TabType;
  onPress: (tab: TabType) => void;
}) => {
  return (
    <TouchableOpacity
      onPress={() => onPress(text)}
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        width: 110.15,
        height: 38.41,
        backgroundColor: isActive ? '#993700' : '#FFE948',
        borderRadius: Theme.radius.radius_full,
        borderWidth: 2,
        borderColor: '#C18500',
        shadowColor: '#FFE948',
        shadowOffset: {
          width: 0,
          height: 8,
        },
        shadowOpacity: 0.46,
        shadowRadius: 11.14,

        elevation: 17,
      }}>
      <TextApp
        text={text}
        preset={'text_xs_semibold'}
        textColor={isActive ? '#FFEE63' : '#6A2000'}
      />
    </TouchableOpacity>
  );
};
const InfoItem = ({
  title,
  value,
  icon,
}: {
  title: string;
  value: string;
  icon?: any;
}) => (
  <View style={styles.infoItem}>
    <Image
      source={Images.frameStreak}
      style={styles.infoItemImage}
      resizeMode="contain"
    />
    <TextApp
      text={title}
      preset="text_xs_medium"
      textColor="#773704"
      style={styles.infoTitle}
    />
    {icon && <Image source={icon} style={styles.infoIcon} />}
    <TextApp
      preset={
        title === 'Streak'
          ? 'display_md_semibold'
          : icon
            ? 'text_sm_semibold'
            : 'display_xs_semibold'
      }
      textColor="#773704"
      text={value}
      style={
        title === 'Streak'
          ? styles.infoValueStreak
          : icon
            ? styles.infoUnit
            : styles.infoValueOther
      }
    />
    <TextApp
      preset="text_xs_medium"
      textColor="#773704"
      text={title === 'Streak' ? 'days' : ''}
      style={styles.infoUnit}
    />
  </View>
);

const InfoBox = ({
  streak,
  pearl,
  exp,
}: {
  streak: string | number;
  pearl: string | number;
  exp: string | number;
}) => (
  <View style={styles.infoBox}>
    <InfoItem title="Streak" value={streak.toString()} />
    <InfoItem title="Pearl" value={pearl.toString()} icon={Icons.gem} />
    <InfoItem title="Exp" value={exp.toString()} />
  </View>
);

interface BoardProfileProps {
  streak: string | number;
  pearl: string | number;
  exp: string | number;
}

const BoardProfile: React.FC<BoardProfileProps> = ({streak, pearl, exp}) => {
  const [activeTab, setActiveTab] = useState<TabType>('Progresses');

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  return (
    <ImageBackground
      source={Images.boardProfile}
      resizeMode={'stretch'}
      style={styles.container}>
      <View style={styles.tabContainer}>
        <ButtonTab
          text="Progresses"
          isActive={activeTab === 'Progresses'}
          onPress={handleTabPress}
        />
        <ButtonTab
          text="Archievements"
          isActive={activeTab === 'Archievements'}
          onPress={handleTabPress}
        />
        <ButtonTab
          text="Wardrobe"
          isActive={activeTab === 'Wardrobe'}
          onPress={handleTabPress}
        />
      </View>

      <View
        style={{
          flex: 1,
          marginTop: 16,
          // marginHorizontal: 40,
        }}>
        {/*<ScrollView showsVerticalScrollIndicator={false} style={{flex: 1}}>*/}
        {activeTab == 'Progresses' && (
          <>
            <InfoBox streak={streak} pearl={pearl} exp={exp} />
            <Image
              source={require('../../assets/images/skillProgressdemo.webp')}
              style={{
                width: 310,
                height: 256,
                alignSelf: 'center',
                marginTop: 36,
              }}
              resizeMode={'contain'}
            />
          </>
        )}
        {activeTab == 'Archievements' && (
          <Image
            source={require('../../assets/images/archie.webp')}
            style={{
              width: 375,
              height: 371,
              alignSelf: 'center',
              marginTop: 6,
            }}
            resizeMode={'contain'}
          />
        )}
        {activeTab == 'Wardrobe' && <Wardrobe />}
        {/*</ScrollView>*/}
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginTop: isAndroid ? 16 : 22,
    alignItems: 'center',
  },
  infoBox: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 30,
  },
  infoItem: {
    alignItems: 'center',
  },
  infoItemImage: {
    width: isAndroid ? 90 : 100.14,
    height: 85.65,
  },
  infoTitle: {
    position: 'absolute',
    top: 6,
  },
  infoValueStreak: {
    position: 'absolute',
    top: isAndroid ? 8 : 20,
  },
  infoValueOther: {
    position: 'absolute',
    top: isAndroid ? 20 : 26,
  },
  infoIcon: {
    position: 'absolute',
    top: 28,
    width: 24,
    height: 24,
  },
  infoUnit: {
    position: 'absolute',
    bottom: 6,
  },
});

export default BoardProfile;
