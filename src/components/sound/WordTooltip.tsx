import React, {useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Popover from 'react-native-popover-view';
import Animated, {FadeIn, FadeOut} from 'react-native-reanimated';
import {useTTS} from '../../hooks/useTTS.tsx';
import {Theme} from '../../themes';
import TextApp from '../TextApp';
import {removePunctuation} from '../../utils/index.ts';
import {processPhonemeScores} from '../../utils/processPhonemeScores.ts';

interface WordTooltipProps {
  word: string;
  styleText?: any;
  textColor: string;
  wordInfo?: any;
}

const WordTooltip: React.FC<WordTooltipProps> = ({
  word,
  styleText,
  textColor,
  wordInfo,
}) => {
  const [visible, setVisible] = useState(false);
  const popoverRef = useRef(null);
  const {speakWord} = useTTS();
  const result = processPhonemeScores(wordInfo.phoneme_scores);
  return (
    <Popover
      ref={popoverRef}
      isVisible={visible}
      popoverStyle={styles.conatiner}
      onRequestClose={() => setVisible(false)}
      from={
        <TouchableOpacity
          onPress={() => {
            speakWord(removePunctuation(word));
            setVisible(true);
          }}>
          <TextApp
            preset="text_xl_semibold"
            style={[styles.text, styleText]}
            textColor={textColor}
            text={word}
          />
        </TouchableOpacity>
      }>
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        style={styles.conatiner}>
        <TextApp preset="text_md_semibold" text={word} style={styles.word} />

        {/*<TextApp*/}
        {/*  preset="text_sm_semibold"*/}
        {/*  text={result?.soundsLikeIPAString}*/}
        {/*  style={styles.word}*/}
        {/*  textColor={'#4CA30D'}*/}
        {/*/>*/}

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          {result?.textSoundsLike.map((i, idx) => (
            <TextApp
              key={idx}
              preset="text_sm_semibold"
              text={i?.text === '-' ? '' : i?.text}
              style={[
                styles.word,
                {marginLeft: 2, textDecorationLine: 'underline'},
              ]}
              textColor={textColor}
            />
          ))}
        </View>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          {result?.textSegments.map((i, idx) => (
            <TextApp
              key={idx}
              preset="text_sm_semibold"
              text={i?.text || ''}
              style={[styles.word, {marginLeft: 2}]}
              textColor={'#000000'}
            />
          ))}
        </View>
        <TextApp
          preset="text_sm_semibold"
          text={wordInfo?.meaning}
          style={styles.meaning}
        />
      </Animated.View>
    </Popover>
  );
};
const styles = StyleSheet.create({
  conatiner: {
    padding: 12,
    backgroundColor: Theme.colors.background,
    borderRadius: 10,
    alignItems: 'center',
    borderColor: Theme.colors.border,
  },
  text: {
    // color: '#DE773D',
    marginLeft: 4,

    textDecorationLine: 'underline',
    letterSpacing: 0.5,
    justifyContent: 'space-around',
    textAlign: 'center',
    lineHeight: 30,
  },
  word: {
    color: Theme.colors.blue['700'],

    // lineHeight: 32,
  },
  ipa: {
    color: Theme.colors.gray['700'],
    lineHeight: 18,
  },
  meaning: {
    color: Theme.colors.textPrimary,
  },
  speaker: {
    backgroundColor: Theme.colors.gray['300'],
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 5,
    marginTop: 5,
  },
  speakerImg: {height: 32, width: 32},
});
export default React.memo(WordTooltip);
