type PhonemeScore = {
  score: number;
  score_grade: string;
  phoneme: string;
  phoneme_ipa: string;
  sounds_like: string;
  sounds_like_ipa: string;
};

type TextSegment = {
  text: string;
  color: string;
};

const getColorByGrade = (grade?: string): string => {
  switch (grade) {
    case 'A':
    case 'B':
      return '#4CA30D';
    case 'C':
      return '#CA8504';
    case 'D':
      return '#D92D20';
    default:
      return '#181D27';
  }
};

export const processPhonemeScores = (phonemeScores: PhonemeScore[]) => {
  if (!phonemeScores) {
    return;
  }
  const phonemeIPAString =
    '\\' + phonemeScores.map(p => p.phoneme_ipa).join('') + '\\';
  const soundsLikeIPAString =
    '\\' +
    phonemeScores
      .map(p => (p.sounds_like_ipa !== '-' ? p.sounds_like_ipa : ''))
      .join('') +
    '\\';

  const textSegments: TextSegment[] = phonemeScores.map(p => ({
    text: p.phoneme_ipa,
    color: getColorByGrade(p.score_grade),
  }));
  const textSoundsLike: TextSegment[] = phonemeScores.map(p => ({
    text: p.sounds_like_ipa,
    color: getColorByGrade(p.score_grade),
  }));
  return {
    phonemeIPAString,
    soundsLikeIPAString,
    textSegments,
    textSoundsLike,
  };
};
