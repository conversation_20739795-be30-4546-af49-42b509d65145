import {createSlice} from '@reduxjs/toolkit';
import {fetchCountMessage, fetchDataInfo, fetchReadMessage} from './fetchData';

interface ProfileSliceData {
  data?: ProfileData;
  loading: boolean;
  error: string;
  countNotification: number;
  isPlayBgSound: boolean;
  character: {
    shirtId: number;
    pantsId: number;
    accessoryId: number;
  };
}

const initialState: ProfileSliceData = {
  data: undefined,
  loading: false,
  error: '',
  countNotification: 0,
  isPlayBgSound: true,
  character: {
    shirtId: 1,
    pantsId: 1,
    accessoryId: 1,
  },
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    resetDataProfile: () => initialState,
    controllerSoundBg: state => {
      state.isPlayBgSound = !state.isPlayBgSound;
    },
    changeCharacter: (state, action) => {
      state.character = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchDataInfo.pending, state => {
        state.loading = true;
        state.error = '';
      })
      .addCase(fetchDataInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action?.payload;
      })
      .addCase(fetchDataInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchCountMessage.fulfilled, (state, action) => {
        state.countNotification = action.payload?.data;
      })
      .addCase(fetchReadMessage.fulfilled, state => {
        state.countNotification = state.countNotification - 1;
      });
  },
});
export const {controllerSoundBg, changeCharacter} = profileSlice.actions;
export default profileSlice.reducer;
