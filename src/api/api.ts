import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {API_BASE_URL} from '@env';
import {resetAndNavigate} from '../navigation/NavigationServices.ts';
import {APP_SCREEN} from '../navigation/screenType.ts';
import i18n from '../i18n';

let _store: any;
let _persistor: any;

export const injectStore = (store: any, persistor: any) => {
  _store = store;
  _persistor = persistor;
};

const createAxiosInstance = (baseURL: string = API_BASE_URL): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  instance.interceptors.request.use(
    (config: AxiosRequestConfig | any) => {
      const state = _store?.getState?.();
      const token = state?.auth?.token;
      config.headers['accept-language'] = i18n.language || 'en';
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    error => Promise.reject(error),
  );

  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    error => {
      const {status} = error.response || {};
      if ((status === 401 || status === 403) && _store) {
        _store.dispatch({type: 'RESET_STATE'});
        _persistor?.purge?.();
        resetAndNavigate(APP_SCREEN.AUTH);
      }
      return Promise.reject(error);
    },
  );

  return instance;
};

const api = createAxiosInstance();

export {api, createAxiosInstance};
