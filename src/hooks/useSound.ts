import {useEffect, useRef, useState} from 'react';
import SoundPlayer from 'react-native-sound-player';
import {API_INTEGRATE_URL} from '@env';
import HapticFeedback from 'react-native-haptic-feedback';

const useSound = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState<number | null>(null);
  const [currentUri, setCurrentUri] = useState<string | null>(null);

  const loopFileRef = useRef<{filename: string; extension: string} | null>(
    null,
  );
  const shouldLoopRef = useRef<boolean>(false);

  const changeSource = (uri: string) => {
    const fullUri = `${API_INTEGRATE_URL}files/get-by-path?path=${uri}`;

    stop();
    setCurrentUri(fullUri);
    setDuration(null);

    try {
      SoundPlayer.playUrl(fullUri);
      setIsPlaying(true);
      shouldLoopRef.current = false;
    } catch (e) {
      console.log('Không thể play URL:', e);
      setIsPlaying(false);
    }
  };

  const play = () => {
    try {
      SoundPlayer.play();
      setIsPlaying(true);
    } catch (e) {
      console.log('play() lỗi:', e);
    }
  };

  const pause = () => {
    try {
      SoundPlayer.pause();
      setIsPlaying(false);
    } catch (e) {
      console.log('pause() lỗi:', e);
    }
  };

  const stop = () => {
    try {
      SoundPlayer.stop();
      setIsPlaying(false);
      shouldLoopRef.current = false;
      loopFileRef.current = null;
    } catch (e) {
      console.log('stop() lỗi:', e);
    }
  };

  const playLocalFile = (
    filename: string,
    activeHaptic: boolean = false,
    loop?: boolean,
  ) => {
    try {
      if (activeHaptic) {
        HapticFeedback.trigger('notificationSuccess', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
      }

      const cleanFilename = filename.replace(/\.mp3$/, '');
      SoundPlayer.playSoundFile(cleanFilename, 'mp3');
      setIsPlaying(true);

      if (loop) {
        loopFileRef.current = {filename: cleanFilename, extension: 'mp3'};
        shouldLoopRef.current = true;
      } else {
        shouldLoopRef.current = false;
        loopFileRef.current = null;
      }
    } catch (e) {
      console.log('playLocalFile() lỗi:', e);
      setIsPlaying(false);
    }
  };

  const playLocalFileWithHaptic = (filename: string) => {
    let beatMap = [
      0.26702947845804986, 0.3599092970521542, 0.4179591836734694,
      0.592108843537415, 1.4164172335600906,
    ];
    try {
      SoundPlayer.playSoundFile(filename, 'mp3');

      const triggered = new Set<number>();
      const startTime = Date.now();

      const interval = setInterval(() => {
        const elapsedSec = (Date.now() - startTime) / 1000;

        beatMap.forEach(time => {
          if (elapsedSec >= time && !triggered.has(time)) {
            triggered.add(time);
            HapticFeedback.trigger('impactHeavy', {
              enableVibrateFallback: true,
              ignoreAndroidSystemSettings: false,
            });
          }
        });

        if (elapsedSec > Math.max(...beatMap) + 1) {
          clearInterval(interval);
        }
      }, 30);
    } catch (e) {
      console.log('playLocalFileWithHaptic lỗi:', e);
    }
  };

  useEffect(() => {
    const loadingSub = SoundPlayer.addEventListener(
      'FinishedLoadingURL',
      async () => {
        try {
          const info = await SoundPlayer.getInfo();
          setDuration(info.duration);
        } catch (err) {
          console.log('getInfo() lỗi:', err);
        }
      },
    );

    const playingSub = SoundPlayer.addEventListener('FinishedPlaying', () => {
      setIsPlaying(false);

      if (shouldLoopRef.current && loopFileRef.current) {
        try {
          SoundPlayer.playSoundFile(
            loopFileRef.current.filename,
            loopFileRef.current.extension,
          );
          setIsPlaying(true);
        } catch (e) {
          console.log('Lặp lại file local lỗi:', e);
        }
      }
    });

    return () => {
      loadingSub.remove();
      playingSub.remove();
    };
  }, []);

  return {
    isPlaying,
    duration,
    changeSource,
    play,
    pause,
    stop,
    playLocalFile,
    playLocalFileWithHaptic,
  };
};

export default useSound;
