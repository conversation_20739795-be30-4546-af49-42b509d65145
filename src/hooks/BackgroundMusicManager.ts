import {AppState, AppStateStatus} from 'react-native';
import SoundPlayer from 'react-native-sound-player';

type AudioTrack = {filename: string; extension: string};

class BackgroundMusicManager {
  private isEnabled: boolean = true;
  private isPlaying: boolean = false;
  private currentTrack: AudioTrack | null = null;
  private wasPlayingBeforeBackground: boolean = false;
  private appState: AppStateStatus = 'active';
  private listeners: Set<() => void> = new Set();

  constructor() {
    this.setupAppStateListener();
    this.setupSoundPlayerListener();
  }

  public play(filename: string, extension: string = 'mp3') {
    if (!this.isEnabled) {
      return;
    }

    this.currentTrack = {filename, extension};
    try {
      SoundPlayer.playSoundFile(filename, extension);
      this.isPlaying = true;
      this.notify();
    } catch (e) {
      console.warn('[BackgroundMusicManager] Play error:', e);
    }
  }

  public pause() {
    try {
      SoundPlayer.pause();
      this.isPlaying = false;
      this.notify();
    } catch (e) {
      console.warn('[BackgroundMusicManager] Pause error:', e);
    }
  }

  public stop() {
    try {
      SoundPlayer.stop();
      this.isPlaying = false;
      this.currentTrack = null;
      this.notify();
    } catch (e) {
      console.warn('[BackgroundMusicManager] Stop error:', e);
    }
  }

  public toggle(enable: boolean) {
    this.isEnabled = enable;

    if (enable && this.currentTrack) {
      this.play(this.currentTrack.filename, this.currentTrack.extension);
    } else {
      this.stop();
    }
  }

  public isEnabledMusic() {
    return this.isEnabled;
  }

  public isPlayingMusic() {
    return this.isPlaying;
  }

  public subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private setupAppStateListener() {
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      this.appState.match(/active/) &&
      nextAppState.match(/inactive|background/)
    ) {
      if (this.isPlaying) {
        this.wasPlayingBeforeBackground = true;
        this.pause();
      }
    }

    if (
      this.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      if (
        this.isEnabled &&
        this.wasPlayingBeforeBackground &&
        this.currentTrack
      ) {
        this.play(this.currentTrack.filename, this.currentTrack.extension);
      }
      this.wasPlayingBeforeBackground = false;
    }

    this.appState = nextAppState;
  };

  private setupSoundPlayerListener() {
    SoundPlayer.addEventListener('FinishedPlaying', () => {
      if (this.isPlaying && this.isEnabled && this.currentTrack) {
        this.play(this.currentTrack.filename, this.currentTrack.extension); // loop
      }
    });
  }

  private notify() {
    this.listeners.forEach(fn => fn());
  }
}

export const backgroundMusicManager = new BackgroundMusicManager();
