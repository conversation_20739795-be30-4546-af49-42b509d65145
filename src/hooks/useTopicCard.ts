import {useCallback, useMemo, useState} from 'react';
import {navigate} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';

const modalData = [
  {
    title: 'Favorite food',
    image: '',
    question: 'What do you like to eat?',
    mission: `🎯 Mission: Tell Ch<PERSON>py your 3 \nfavorite foods and ask him!`,
    reward: "You'll get 20",
  },
  {
    title: 'Favorite color',
    image: '',
    question: 'What is your favorite color?',
    mission: '🎯 Mission: Share your top 3 \nfavorite colors with <PERSON><PERSON><PERSON>!',
    reward: "You'll get 15",
  },
  {
    title: 'Your pet',
    image: '',
    question: 'Let’s talk about pets!',
    mission:
      '🎯 Mission: Tell Ch<PERSON><PERSON> what pet you \nhave or want and what it can do.',
    reward: "You'll get 25",
  },
  {
    title: 'Favorite place',
    image: '',
    question: 'Where do you like to go?',
    mission: '🎯 Mission: Share your top 3 \nfavorite places with <PERSON><PERSON><PERSON>!',
    reward: "You'll get 30",
  },
];

export const useTopicCard = () => {
  const [topicCardData, setTopicCardData] = useState<any[]>(modalData);
  const [modalState, setModalState] = useState({
    visible: false,
    content: null as RolePlayCard | null,
  });
  const [uiState, setUIState] = useState({
    showMission: false,
    quitModal: false,
    ccActive: false,
    ideaActive: false,
    finishModal: false,
  });
  const handleShowFinishModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      finishModal: true,
    }));
  }, []);

  const handleCloseFinishModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      finishModal: false,
    }));
  }, []);

  const handleSeeFunTalk = () => {
    setUIState(prev => ({
      ...prev,
      finishModal: false,
    }));
    setTimeout(() => {
      navigate(APP_SCREEN.FULL_CONVERSATION);
    }, 300);
  };

  const handleCloseModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const handleStartNow = useCallback(async () => {
    handleCloseModal();
    setUIState(prev => ({
      ...prev,
      showMission: true,
    }));
  }, [handleCloseModal]);

  const handleChooseCard = useCallback((item: any) => {
    setModalState(prev => ({
      ...prev,
      content: item,
    }));
    setTimeout(() => {
      setModalState(prev => ({
        ...prev,
        visible: true,
      }));
    }, 200);
  }, []);

  return useMemo(
    () => ({
      modalVisible: modalState.visible,
      modalContent: modalState.content,
      showMission: uiState.showMission,
      quitModal: uiState.quitModal,
      ccActive: uiState.ccActive,
      ideaActive: uiState.ideaActive,
      finishModal: uiState.finishModal,
      topicCardData,
      handleCloseModal,
      handleStartNow,
      handleChooseCard,
      handleShowFinishModal,
      handleCloseFinishModal,
      handleSeeFunTalk,
    }),
    [
      modalState.visible,
      modalState.content,
      uiState.showMission,
      uiState.quitModal,
      uiState.ccActive,
      uiState.ideaActive,
      uiState.finishModal,
      topicCardData,
      handleCloseModal,
      handleStartNow,
      handleChooseCard,
      handleShowFinishModal,
      handleCloseFinishModal,
      handleSeeFunTalk,
    ],
  );
};
